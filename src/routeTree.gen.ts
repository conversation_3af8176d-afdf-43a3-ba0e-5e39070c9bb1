/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AuthRouteImport } from './routes/_auth'
import { Route as AuthIndexRouteImport } from './routes/_auth/index'
import { Route as AuthenticationCallbackRouteImport } from './routes/authentication/callback'
import { Route as AuthDataSummaryRouteRouteImport } from './routes/_auth/data-summary/route'
import { Route as AuthBasicReportRouteRouteImport } from './routes/_auth/basic-report/route'
import { Route as AuthDataSummaryIndexRouteImport } from './routes/_auth/data-summary/index'
import { Route as AuthBasicReportIndexRouteImport } from './routes/_auth/basic-report/index'
import { Route as AuthDataSummaryMonthlyReportRouteRouteImport } from './routes/_auth/data-summary/monthly-report/route'
import { Route as AuthDataSummaryInvestmentPlanRouteRouteImport } from './routes/_auth/data-summary/investment-plan/route'
import { Route as AuthDataSummaryCompletionStatusRouteRouteImport } from './routes/_auth/data-summary/completion-status/route'
import { Route as AuthBasicReportPostEvaluationRouteRouteImport } from './routes/_auth/basic-report/post-evaluation/route'
import { Route as AuthBasicReportFixedAssetsRouteRouteImport } from './routes/_auth/basic-report/fixed-assets/route'
import { Route as AuthBasicReportEquityProjectsRouteRouteImport } from './routes/_auth/basic-report/equity-projects/route'
import { Route as AuthDataSummaryMonthlyReportIndexRouteImport } from './routes/_auth/data-summary/monthly-report/index'
import { Route as AuthDataSummaryInvestmentPlanIndexRouteImport } from './routes/_auth/data-summary/investment-plan/index'
import { Route as AuthDataSummaryCompletionStatusIndexRouteImport } from './routes/_auth/data-summary/completion-status/index'
import { Route as AuthBasicReportPostEvaluationIndexRouteImport } from './routes/_auth/basic-report/post-evaluation/index'
import { Route as AuthBasicReportFixedAssetsIndexRouteImport } from './routes/_auth/basic-report/fixed-assets/index'
import { Route as AuthBasicReportEquityProjectsIndexRouteImport } from './routes/_auth/basic-report/equity-projects/index'
import { Route as AuthDataSummaryMonthlyReportCreateRouteImport } from './routes/_auth/data-summary/monthly-report/create'
import { Route as AuthDataSummaryInvestmentPlanTableRouteImport } from './routes/_auth/data-summary/investment-plan/table'
import { Route as AuthBasicReportPostEvaluationCreateRouteImport } from './routes/_auth/basic-report/post-evaluation/create'
import { Route as AuthBasicReportFixedAssetsCreateRouteImport } from './routes/_auth/basic-report/fixed-assets/create'
import { Route as AuthBasicReportEquityProjectsCreateRouteImport } from './routes/_auth/basic-report/equity-projects/create'
import { Route as AuthDataSummaryCompletionStatusQuarterRouteRouteImport } from './routes/_auth/data-summary/completion-status/$quarter/route'
import { Route as AuthDataSummaryCompletionStatusQuarterIndexRouteImport } from './routes/_auth/data-summary/completion-status/$quarter/index'
import { Route as AuthDataSummaryMonthlyReportIdUpdateRouteImport } from './routes/_auth/data-summary/monthly-report/$id/update'
import { Route as AuthBasicReportPostEvaluationIdUpdateRouteImport } from './routes/_auth/basic-report/post-evaluation/$id/update'
import { Route as AuthBasicReportFixedAssetsIdUpdateRouteImport } from './routes/_auth/basic-report/fixed-assets/$id/update'
import { Route as AuthBasicReportEquityProjectsIdUpdateRouteImport } from './routes/_auth/basic-report/equity-projects/$id/update'
import { Route as AuthDataSummaryCompletionStatusQuarterTableRouteRouteImport } from './routes/_auth/data-summary/completion-status/$quarter/$table/route'
import { Route as AuthDataSummaryInvestmentPlanTableTableNameUpdateRouteImport } from './routes/_auth/data-summary/investment-plan/table/$tableName/update'
import { Route as AuthDataSummaryInvestmentPlanTableTableNameListRouteImport } from './routes/_auth/data-summary/investment-plan/table/$tableName/list'
import { Route as AuthDataSummaryCompletionStatusQuarterTableUpdateRouteImport } from './routes/_auth/data-summary/completion-status/$quarter/$table/update'
import { Route as AuthDataSummaryCompletionStatusQuarterTableListRouteImport } from './routes/_auth/data-summary/completion-status/$quarter/$table/list'

const AuthRoute = AuthRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthIndexRoute = AuthIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthRoute,
} as any)
const AuthenticationCallbackRoute = AuthenticationCallbackRouteImport.update({
  id: '/authentication/callback',
  path: '/authentication/callback',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthDataSummaryRouteRoute = AuthDataSummaryRouteRouteImport.update({
  id: '/data-summary',
  path: '/data-summary',
  getParentRoute: () => AuthRoute,
} as any)
const AuthBasicReportRouteRoute = AuthBasicReportRouteRouteImport.update({
  id: '/basic-report',
  path: '/basic-report',
  getParentRoute: () => AuthRoute,
} as any)
const AuthDataSummaryIndexRoute = AuthDataSummaryIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthDataSummaryRouteRoute,
} as any)
const AuthBasicReportIndexRoute = AuthBasicReportIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthBasicReportRouteRoute,
} as any)
const AuthDataSummaryMonthlyReportRouteRoute =
  AuthDataSummaryMonthlyReportRouteRouteImport.update({
    id: '/monthly-report',
    path: '/monthly-report',
    getParentRoute: () => AuthDataSummaryRouteRoute,
  } as any)
const AuthDataSummaryInvestmentPlanRouteRoute =
  AuthDataSummaryInvestmentPlanRouteRouteImport.update({
    id: '/investment-plan',
    path: '/investment-plan',
    getParentRoute: () => AuthDataSummaryRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusRouteRoute =
  AuthDataSummaryCompletionStatusRouteRouteImport.update({
    id: '/completion-status',
    path: '/completion-status',
    getParentRoute: () => AuthDataSummaryRouteRoute,
  } as any)
const AuthBasicReportPostEvaluationRouteRoute =
  AuthBasicReportPostEvaluationRouteRouteImport.update({
    id: '/post-evaluation',
    path: '/post-evaluation',
    getParentRoute: () => AuthBasicReportRouteRoute,
  } as any)
const AuthBasicReportFixedAssetsRouteRoute =
  AuthBasicReportFixedAssetsRouteRouteImport.update({
    id: '/fixed-assets',
    path: '/fixed-assets',
    getParentRoute: () => AuthBasicReportRouteRoute,
  } as any)
const AuthBasicReportEquityProjectsRouteRoute =
  AuthBasicReportEquityProjectsRouteRouteImport.update({
    id: '/equity-projects',
    path: '/equity-projects',
    getParentRoute: () => AuthBasicReportRouteRoute,
  } as any)
const AuthDataSummaryMonthlyReportIndexRoute =
  AuthDataSummaryMonthlyReportIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthDataSummaryMonthlyReportRouteRoute,
  } as any)
const AuthDataSummaryInvestmentPlanIndexRoute =
  AuthDataSummaryInvestmentPlanIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthDataSummaryInvestmentPlanRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusIndexRoute =
  AuthDataSummaryCompletionStatusIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthDataSummaryCompletionStatusRouteRoute,
  } as any)
const AuthBasicReportPostEvaluationIndexRoute =
  AuthBasicReportPostEvaluationIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthBasicReportPostEvaluationRouteRoute,
  } as any)
const AuthBasicReportFixedAssetsIndexRoute =
  AuthBasicReportFixedAssetsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthBasicReportFixedAssetsRouteRoute,
  } as any)
const AuthBasicReportEquityProjectsIndexRoute =
  AuthBasicReportEquityProjectsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthBasicReportEquityProjectsRouteRoute,
  } as any)
const AuthDataSummaryMonthlyReportCreateRoute =
  AuthDataSummaryMonthlyReportCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthDataSummaryMonthlyReportRouteRoute,
  } as any)
const AuthDataSummaryInvestmentPlanTableRoute =
  AuthDataSummaryInvestmentPlanTableRouteImport.update({
    id: '/table',
    path: '/table',
    getParentRoute: () => AuthDataSummaryInvestmentPlanRouteRoute,
  } as any)
const AuthBasicReportPostEvaluationCreateRoute =
  AuthBasicReportPostEvaluationCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthBasicReportPostEvaluationRouteRoute,
  } as any)
const AuthBasicReportFixedAssetsCreateRoute =
  AuthBasicReportFixedAssetsCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthBasicReportFixedAssetsRouteRoute,
  } as any)
const AuthBasicReportEquityProjectsCreateRoute =
  AuthBasicReportEquityProjectsCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthBasicReportEquityProjectsRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusQuarterRouteRoute =
  AuthDataSummaryCompletionStatusQuarterRouteRouteImport.update({
    id: '/$quarter',
    path: '/$quarter',
    getParentRoute: () => AuthDataSummaryCompletionStatusRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusQuarterIndexRoute =
  AuthDataSummaryCompletionStatusQuarterIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthDataSummaryCompletionStatusQuarterRouteRoute,
  } as any)
const AuthDataSummaryMonthlyReportIdUpdateRoute =
  AuthDataSummaryMonthlyReportIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthDataSummaryMonthlyReportRouteRoute,
  } as any)
const AuthBasicReportPostEvaluationIdUpdateRoute =
  AuthBasicReportPostEvaluationIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthBasicReportPostEvaluationRouteRoute,
  } as any)
const AuthBasicReportFixedAssetsIdUpdateRoute =
  AuthBasicReportFixedAssetsIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthBasicReportFixedAssetsRouteRoute,
  } as any)
const AuthBasicReportEquityProjectsIdUpdateRoute =
  AuthBasicReportEquityProjectsIdUpdateRouteImport.update({
    id: '/$id/update',
    path: '/$id/update',
    getParentRoute: () => AuthBasicReportEquityProjectsRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusQuarterTableRouteRoute =
  AuthDataSummaryCompletionStatusQuarterTableRouteRouteImport.update({
    id: '/$table',
    path: '/$table',
    getParentRoute: () => AuthDataSummaryCompletionStatusQuarterRouteRoute,
  } as any)
const AuthDataSummaryInvestmentPlanTableTableNameUpdateRoute =
  AuthDataSummaryInvestmentPlanTableTableNameUpdateRouteImport.update({
    id: '/$tableName/update',
    path: '/$tableName/update',
    getParentRoute: () => AuthDataSummaryInvestmentPlanTableRoute,
  } as any)
const AuthDataSummaryInvestmentPlanTableTableNameListRoute =
  AuthDataSummaryInvestmentPlanTableTableNameListRouteImport.update({
    id: '/$tableName/list',
    path: '/$tableName/list',
    getParentRoute: () => AuthDataSummaryInvestmentPlanTableRoute,
  } as any)
const AuthDataSummaryCompletionStatusQuarterTableUpdateRoute =
  AuthDataSummaryCompletionStatusQuarterTableUpdateRouteImport.update({
    id: '/update',
    path: '/update',
    getParentRoute: () => AuthDataSummaryCompletionStatusQuarterTableRouteRoute,
  } as any)
const AuthDataSummaryCompletionStatusQuarterTableListRoute =
  AuthDataSummaryCompletionStatusQuarterTableListRouteImport.update({
    id: '/list',
    path: '/list',
    getParentRoute: () => AuthDataSummaryCompletionStatusQuarterTableRouteRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/basic-report': typeof AuthBasicReportRouteRouteWithChildren
  '/data-summary': typeof AuthDataSummaryRouteRouteWithChildren
  '/authentication/callback': typeof AuthenticationCallbackRoute
  '/': typeof AuthIndexRoute
  '/basic-report/equity-projects': typeof AuthBasicReportEquityProjectsRouteRouteWithChildren
  '/basic-report/fixed-assets': typeof AuthBasicReportFixedAssetsRouteRouteWithChildren
  '/basic-report/post-evaluation': typeof AuthBasicReportPostEvaluationRouteRouteWithChildren
  '/data-summary/completion-status': typeof AuthDataSummaryCompletionStatusRouteRouteWithChildren
  '/data-summary/investment-plan': typeof AuthDataSummaryInvestmentPlanRouteRouteWithChildren
  '/data-summary/monthly-report': typeof AuthDataSummaryMonthlyReportRouteRouteWithChildren
  '/basic-report/': typeof AuthBasicReportIndexRoute
  '/data-summary/': typeof AuthDataSummaryIndexRoute
  '/data-summary/completion-status/$quarter': typeof AuthDataSummaryCompletionStatusQuarterRouteRouteWithChildren
  '/basic-report/equity-projects/create': typeof AuthBasicReportEquityProjectsCreateRoute
  '/basic-report/fixed-assets/create': typeof AuthBasicReportFixedAssetsCreateRoute
  '/basic-report/post-evaluation/create': typeof AuthBasicReportPostEvaluationCreateRoute
  '/data-summary/investment-plan/table': typeof AuthDataSummaryInvestmentPlanTableRouteWithChildren
  '/data-summary/monthly-report/create': typeof AuthDataSummaryMonthlyReportCreateRoute
  '/basic-report/equity-projects/': typeof AuthBasicReportEquityProjectsIndexRoute
  '/basic-report/fixed-assets/': typeof AuthBasicReportFixedAssetsIndexRoute
  '/basic-report/post-evaluation/': typeof AuthBasicReportPostEvaluationIndexRoute
  '/data-summary/completion-status/': typeof AuthDataSummaryCompletionStatusIndexRoute
  '/data-summary/investment-plan/': typeof AuthDataSummaryInvestmentPlanIndexRoute
  '/data-summary/monthly-report/': typeof AuthDataSummaryMonthlyReportIndexRoute
  '/data-summary/completion-status/$quarter/$table': typeof AuthDataSummaryCompletionStatusQuarterTableRouteRouteWithChildren
  '/basic-report/equity-projects/$id/update': typeof AuthBasicReportEquityProjectsIdUpdateRoute
  '/basic-report/fixed-assets/$id/update': typeof AuthBasicReportFixedAssetsIdUpdateRoute
  '/basic-report/post-evaluation/$id/update': typeof AuthBasicReportPostEvaluationIdUpdateRoute
  '/data-summary/monthly-report/$id/update': typeof AuthDataSummaryMonthlyReportIdUpdateRoute
  '/data-summary/completion-status/$quarter/': typeof AuthDataSummaryCompletionStatusQuarterIndexRoute
  '/data-summary/completion-status/$quarter/$table/list': typeof AuthDataSummaryCompletionStatusQuarterTableListRoute
  '/data-summary/completion-status/$quarter/$table/update': typeof AuthDataSummaryCompletionStatusQuarterTableUpdateRoute
  '/data-summary/investment-plan/table/$tableName/list': typeof AuthDataSummaryInvestmentPlanTableTableNameListRoute
  '/data-summary/investment-plan/table/$tableName/update': typeof AuthDataSummaryInvestmentPlanTableTableNameUpdateRoute
}
export interface FileRoutesByTo {
  '/authentication/callback': typeof AuthenticationCallbackRoute
  '/': typeof AuthIndexRoute
  '/basic-report': typeof AuthBasicReportIndexRoute
  '/data-summary': typeof AuthDataSummaryIndexRoute
  '/basic-report/equity-projects/create': typeof AuthBasicReportEquityProjectsCreateRoute
  '/basic-report/fixed-assets/create': typeof AuthBasicReportFixedAssetsCreateRoute
  '/basic-report/post-evaluation/create': typeof AuthBasicReportPostEvaluationCreateRoute
  '/data-summary/investment-plan/table': typeof AuthDataSummaryInvestmentPlanTableRouteWithChildren
  '/data-summary/monthly-report/create': typeof AuthDataSummaryMonthlyReportCreateRoute
  '/basic-report/equity-projects': typeof AuthBasicReportEquityProjectsIndexRoute
  '/basic-report/fixed-assets': typeof AuthBasicReportFixedAssetsIndexRoute
  '/basic-report/post-evaluation': typeof AuthBasicReportPostEvaluationIndexRoute
  '/data-summary/completion-status': typeof AuthDataSummaryCompletionStatusIndexRoute
  '/data-summary/investment-plan': typeof AuthDataSummaryInvestmentPlanIndexRoute
  '/data-summary/monthly-report': typeof AuthDataSummaryMonthlyReportIndexRoute
  '/data-summary/completion-status/$quarter/$table': typeof AuthDataSummaryCompletionStatusQuarterTableRouteRouteWithChildren
  '/basic-report/equity-projects/$id/update': typeof AuthBasicReportEquityProjectsIdUpdateRoute
  '/basic-report/fixed-assets/$id/update': typeof AuthBasicReportFixedAssetsIdUpdateRoute
  '/basic-report/post-evaluation/$id/update': typeof AuthBasicReportPostEvaluationIdUpdateRoute
  '/data-summary/monthly-report/$id/update': typeof AuthDataSummaryMonthlyReportIdUpdateRoute
  '/data-summary/completion-status/$quarter': typeof AuthDataSummaryCompletionStatusQuarterIndexRoute
  '/data-summary/completion-status/$quarter/$table/list': typeof AuthDataSummaryCompletionStatusQuarterTableListRoute
  '/data-summary/completion-status/$quarter/$table/update': typeof AuthDataSummaryCompletionStatusQuarterTableUpdateRoute
  '/data-summary/investment-plan/table/$tableName/list': typeof AuthDataSummaryInvestmentPlanTableTableNameListRoute
  '/data-summary/investment-plan/table/$tableName/update': typeof AuthDataSummaryInvestmentPlanTableTableNameUpdateRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_auth': typeof AuthRouteWithChildren
  '/_auth/basic-report': typeof AuthBasicReportRouteRouteWithChildren
  '/_auth/data-summary': typeof AuthDataSummaryRouteRouteWithChildren
  '/authentication/callback': typeof AuthenticationCallbackRoute
  '/_auth/': typeof AuthIndexRoute
  '/_auth/basic-report/equity-projects': typeof AuthBasicReportEquityProjectsRouteRouteWithChildren
  '/_auth/basic-report/fixed-assets': typeof AuthBasicReportFixedAssetsRouteRouteWithChildren
  '/_auth/basic-report/post-evaluation': typeof AuthBasicReportPostEvaluationRouteRouteWithChildren
  '/_auth/data-summary/completion-status': typeof AuthDataSummaryCompletionStatusRouteRouteWithChildren
  '/_auth/data-summary/investment-plan': typeof AuthDataSummaryInvestmentPlanRouteRouteWithChildren
  '/_auth/data-summary/monthly-report': typeof AuthDataSummaryMonthlyReportRouteRouteWithChildren
  '/_auth/basic-report/': typeof AuthBasicReportIndexRoute
  '/_auth/data-summary/': typeof AuthDataSummaryIndexRoute
  '/_auth/data-summary/completion-status/$quarter': typeof AuthDataSummaryCompletionStatusQuarterRouteRouteWithChildren
  '/_auth/basic-report/equity-projects/create': typeof AuthBasicReportEquityProjectsCreateRoute
  '/_auth/basic-report/fixed-assets/create': typeof AuthBasicReportFixedAssetsCreateRoute
  '/_auth/basic-report/post-evaluation/create': typeof AuthBasicReportPostEvaluationCreateRoute
  '/_auth/data-summary/investment-plan/table': typeof AuthDataSummaryInvestmentPlanTableRouteWithChildren
  '/_auth/data-summary/monthly-report/create': typeof AuthDataSummaryMonthlyReportCreateRoute
  '/_auth/basic-report/equity-projects/': typeof AuthBasicReportEquityProjectsIndexRoute
  '/_auth/basic-report/fixed-assets/': typeof AuthBasicReportFixedAssetsIndexRoute
  '/_auth/basic-report/post-evaluation/': typeof AuthBasicReportPostEvaluationIndexRoute
  '/_auth/data-summary/completion-status/': typeof AuthDataSummaryCompletionStatusIndexRoute
  '/_auth/data-summary/investment-plan/': typeof AuthDataSummaryInvestmentPlanIndexRoute
  '/_auth/data-summary/monthly-report/': typeof AuthDataSummaryMonthlyReportIndexRoute
  '/_auth/data-summary/completion-status/$quarter/$table': typeof AuthDataSummaryCompletionStatusQuarterTableRouteRouteWithChildren
  '/_auth/basic-report/equity-projects/$id/update': typeof AuthBasicReportEquityProjectsIdUpdateRoute
  '/_auth/basic-report/fixed-assets/$id/update': typeof AuthBasicReportFixedAssetsIdUpdateRoute
  '/_auth/basic-report/post-evaluation/$id/update': typeof AuthBasicReportPostEvaluationIdUpdateRoute
  '/_auth/data-summary/monthly-report/$id/update': typeof AuthDataSummaryMonthlyReportIdUpdateRoute
  '/_auth/data-summary/completion-status/$quarter/': typeof AuthDataSummaryCompletionStatusQuarterIndexRoute
  '/_auth/data-summary/completion-status/$quarter/$table/list': typeof AuthDataSummaryCompletionStatusQuarterTableListRoute
  '/_auth/data-summary/completion-status/$quarter/$table/update': typeof AuthDataSummaryCompletionStatusQuarterTableUpdateRoute
  '/_auth/data-summary/investment-plan/table/$tableName/list': typeof AuthDataSummaryInvestmentPlanTableTableNameListRoute
  '/_auth/data-summary/investment-plan/table/$tableName/update': typeof AuthDataSummaryInvestmentPlanTableTableNameUpdateRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/basic-report'
    | '/data-summary'
    | '/authentication/callback'
    | '/'
    | '/basic-report/equity-projects'
    | '/basic-report/fixed-assets'
    | '/basic-report/post-evaluation'
    | '/data-summary/completion-status'
    | '/data-summary/investment-plan'
    | '/data-summary/monthly-report'
    | '/basic-report/'
    | '/data-summary/'
    | '/data-summary/completion-status/$quarter'
    | '/basic-report/equity-projects/create'
    | '/basic-report/fixed-assets/create'
    | '/basic-report/post-evaluation/create'
    | '/data-summary/investment-plan/table'
    | '/data-summary/monthly-report/create'
    | '/basic-report/equity-projects/'
    | '/basic-report/fixed-assets/'
    | '/basic-report/post-evaluation/'
    | '/data-summary/completion-status/'
    | '/data-summary/investment-plan/'
    | '/data-summary/monthly-report/'
    | '/data-summary/completion-status/$quarter/$table'
    | '/basic-report/equity-projects/$id/update'
    | '/basic-report/fixed-assets/$id/update'
    | '/basic-report/post-evaluation/$id/update'
    | '/data-summary/monthly-report/$id/update'
    | '/data-summary/completion-status/$quarter/'
    | '/data-summary/completion-status/$quarter/$table/list'
    | '/data-summary/completion-status/$quarter/$table/update'
    | '/data-summary/investment-plan/table/$tableName/list'
    | '/data-summary/investment-plan/table/$tableName/update'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/authentication/callback'
    | '/'
    | '/basic-report'
    | '/data-summary'
    | '/basic-report/equity-projects/create'
    | '/basic-report/fixed-assets/create'
    | '/basic-report/post-evaluation/create'
    | '/data-summary/investment-plan/table'
    | '/data-summary/monthly-report/create'
    | '/basic-report/equity-projects'
    | '/basic-report/fixed-assets'
    | '/basic-report/post-evaluation'
    | '/data-summary/completion-status'
    | '/data-summary/investment-plan'
    | '/data-summary/monthly-report'
    | '/data-summary/completion-status/$quarter/$table'
    | '/basic-report/equity-projects/$id/update'
    | '/basic-report/fixed-assets/$id/update'
    | '/basic-report/post-evaluation/$id/update'
    | '/data-summary/monthly-report/$id/update'
    | '/data-summary/completion-status/$quarter'
    | '/data-summary/completion-status/$quarter/$table/list'
    | '/data-summary/completion-status/$quarter/$table/update'
    | '/data-summary/investment-plan/table/$tableName/list'
    | '/data-summary/investment-plan/table/$tableName/update'
  id:
    | '__root__'
    | '/_auth'
    | '/_auth/basic-report'
    | '/_auth/data-summary'
    | '/authentication/callback'
    | '/_auth/'
    | '/_auth/basic-report/equity-projects'
    | '/_auth/basic-report/fixed-assets'
    | '/_auth/basic-report/post-evaluation'
    | '/_auth/data-summary/completion-status'
    | '/_auth/data-summary/investment-plan'
    | '/_auth/data-summary/monthly-report'
    | '/_auth/basic-report/'
    | '/_auth/data-summary/'
    | '/_auth/data-summary/completion-status/$quarter'
    | '/_auth/basic-report/equity-projects/create'
    | '/_auth/basic-report/fixed-assets/create'
    | '/_auth/basic-report/post-evaluation/create'
    | '/_auth/data-summary/investment-plan/table'
    | '/_auth/data-summary/monthly-report/create'
    | '/_auth/basic-report/equity-projects/'
    | '/_auth/basic-report/fixed-assets/'
    | '/_auth/basic-report/post-evaluation/'
    | '/_auth/data-summary/completion-status/'
    | '/_auth/data-summary/investment-plan/'
    | '/_auth/data-summary/monthly-report/'
    | '/_auth/data-summary/completion-status/$quarter/$table'
    | '/_auth/basic-report/equity-projects/$id/update'
    | '/_auth/basic-report/fixed-assets/$id/update'
    | '/_auth/basic-report/post-evaluation/$id/update'
    | '/_auth/data-summary/monthly-report/$id/update'
    | '/_auth/data-summary/completion-status/$quarter/'
    | '/_auth/data-summary/completion-status/$quarter/$table/list'
    | '/_auth/data-summary/completion-status/$quarter/$table/update'
    | '/_auth/data-summary/investment-plan/table/$tableName/list'
    | '/_auth/data-summary/investment-plan/table/$tableName/update'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  AuthenticationCallbackRoute: typeof AuthenticationCallbackRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth/': {
      id: '/_auth/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthIndexRouteImport
      parentRoute: typeof AuthRoute
    }
    '/authentication/callback': {
      id: '/authentication/callback'
      path: '/authentication/callback'
      fullPath: '/authentication/callback'
      preLoaderRoute: typeof AuthenticationCallbackRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth/data-summary': {
      id: '/_auth/data-summary'
      path: '/data-summary'
      fullPath: '/data-summary'
      preLoaderRoute: typeof AuthDataSummaryRouteRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/basic-report': {
      id: '/_auth/basic-report'
      path: '/basic-report'
      fullPath: '/basic-report'
      preLoaderRoute: typeof AuthBasicReportRouteRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/data-summary/': {
      id: '/_auth/data-summary/'
      path: '/'
      fullPath: '/data-summary/'
      preLoaderRoute: typeof AuthDataSummaryIndexRouteImport
      parentRoute: typeof AuthDataSummaryRouteRoute
    }
    '/_auth/basic-report/': {
      id: '/_auth/basic-report/'
      path: '/'
      fullPath: '/basic-report/'
      preLoaderRoute: typeof AuthBasicReportIndexRouteImport
      parentRoute: typeof AuthBasicReportRouteRoute
    }
    '/_auth/data-summary/monthly-report': {
      id: '/_auth/data-summary/monthly-report'
      path: '/monthly-report'
      fullPath: '/data-summary/monthly-report'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportRouteRouteImport
      parentRoute: typeof AuthDataSummaryRouteRoute
    }
    '/_auth/data-summary/investment-plan': {
      id: '/_auth/data-summary/investment-plan'
      path: '/investment-plan'
      fullPath: '/data-summary/investment-plan'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanRouteRouteImport
      parentRoute: typeof AuthDataSummaryRouteRoute
    }
    '/_auth/data-summary/completion-status': {
      id: '/_auth/data-summary/completion-status'
      path: '/completion-status'
      fullPath: '/data-summary/completion-status'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusRouteRouteImport
      parentRoute: typeof AuthDataSummaryRouteRoute
    }
    '/_auth/basic-report/post-evaluation': {
      id: '/_auth/basic-report/post-evaluation'
      path: '/post-evaluation'
      fullPath: '/basic-report/post-evaluation'
      preLoaderRoute: typeof AuthBasicReportPostEvaluationRouteRouteImport
      parentRoute: typeof AuthBasicReportRouteRoute
    }
    '/_auth/basic-report/fixed-assets': {
      id: '/_auth/basic-report/fixed-assets'
      path: '/fixed-assets'
      fullPath: '/basic-report/fixed-assets'
      preLoaderRoute: typeof AuthBasicReportFixedAssetsRouteRouteImport
      parentRoute: typeof AuthBasicReportRouteRoute
    }
    '/_auth/basic-report/equity-projects': {
      id: '/_auth/basic-report/equity-projects'
      path: '/equity-projects'
      fullPath: '/basic-report/equity-projects'
      preLoaderRoute: typeof AuthBasicReportEquityProjectsRouteRouteImport
      parentRoute: typeof AuthBasicReportRouteRoute
    }
    '/_auth/data-summary/monthly-report/': {
      id: '/_auth/data-summary/monthly-report/'
      path: '/'
      fullPath: '/data-summary/monthly-report/'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportIndexRouteImport
      parentRoute: typeof AuthDataSummaryMonthlyReportRouteRoute
    }
    '/_auth/data-summary/investment-plan/': {
      id: '/_auth/data-summary/investment-plan/'
      path: '/'
      fullPath: '/data-summary/investment-plan/'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanIndexRouteImport
      parentRoute: typeof AuthDataSummaryInvestmentPlanRouteRoute
    }
    '/_auth/data-summary/completion-status/': {
      id: '/_auth/data-summary/completion-status/'
      path: '/'
      fullPath: '/data-summary/completion-status/'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusIndexRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusRouteRoute
    }
    '/_auth/basic-report/post-evaluation/': {
      id: '/_auth/basic-report/post-evaluation/'
      path: '/'
      fullPath: '/basic-report/post-evaluation/'
      preLoaderRoute: typeof AuthBasicReportPostEvaluationIndexRouteImport
      parentRoute: typeof AuthBasicReportPostEvaluationRouteRoute
    }
    '/_auth/basic-report/fixed-assets/': {
      id: '/_auth/basic-report/fixed-assets/'
      path: '/'
      fullPath: '/basic-report/fixed-assets/'
      preLoaderRoute: typeof AuthBasicReportFixedAssetsIndexRouteImport
      parentRoute: typeof AuthBasicReportFixedAssetsRouteRoute
    }
    '/_auth/basic-report/equity-projects/': {
      id: '/_auth/basic-report/equity-projects/'
      path: '/'
      fullPath: '/basic-report/equity-projects/'
      preLoaderRoute: typeof AuthBasicReportEquityProjectsIndexRouteImport
      parentRoute: typeof AuthBasicReportEquityProjectsRouteRoute
    }
    '/_auth/data-summary/monthly-report/create': {
      id: '/_auth/data-summary/monthly-report/create'
      path: '/create'
      fullPath: '/data-summary/monthly-report/create'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportCreateRouteImport
      parentRoute: typeof AuthDataSummaryMonthlyReportRouteRoute
    }
    '/_auth/data-summary/investment-plan/table': {
      id: '/_auth/data-summary/investment-plan/table'
      path: '/table'
      fullPath: '/data-summary/investment-plan/table'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanTableRouteImport
      parentRoute: typeof AuthDataSummaryInvestmentPlanRouteRoute
    }
    '/_auth/basic-report/post-evaluation/create': {
      id: '/_auth/basic-report/post-evaluation/create'
      path: '/create'
      fullPath: '/basic-report/post-evaluation/create'
      preLoaderRoute: typeof AuthBasicReportPostEvaluationCreateRouteImport
      parentRoute: typeof AuthBasicReportPostEvaluationRouteRoute
    }
    '/_auth/basic-report/fixed-assets/create': {
      id: '/_auth/basic-report/fixed-assets/create'
      path: '/create'
      fullPath: '/basic-report/fixed-assets/create'
      preLoaderRoute: typeof AuthBasicReportFixedAssetsCreateRouteImport
      parentRoute: typeof AuthBasicReportFixedAssetsRouteRoute
    }
    '/_auth/basic-report/equity-projects/create': {
      id: '/_auth/basic-report/equity-projects/create'
      path: '/create'
      fullPath: '/basic-report/equity-projects/create'
      preLoaderRoute: typeof AuthBasicReportEquityProjectsCreateRouteImport
      parentRoute: typeof AuthBasicReportEquityProjectsRouteRoute
    }
    '/_auth/data-summary/completion-status/$quarter': {
      id: '/_auth/data-summary/completion-status/$quarter'
      path: '/$quarter'
      fullPath: '/data-summary/completion-status/$quarter'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusQuarterRouteRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusRouteRoute
    }
    '/_auth/data-summary/completion-status/$quarter/': {
      id: '/_auth/data-summary/completion-status/$quarter/'
      path: '/'
      fullPath: '/data-summary/completion-status/$quarter/'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusQuarterIndexRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusQuarterRouteRoute
    }
    '/_auth/data-summary/monthly-report/$id/update': {
      id: '/_auth/data-summary/monthly-report/$id/update'
      path: '/$id/update'
      fullPath: '/data-summary/monthly-report/$id/update'
      preLoaderRoute: typeof AuthDataSummaryMonthlyReportIdUpdateRouteImport
      parentRoute: typeof AuthDataSummaryMonthlyReportRouteRoute
    }
    '/_auth/basic-report/post-evaluation/$id/update': {
      id: '/_auth/basic-report/post-evaluation/$id/update'
      path: '/$id/update'
      fullPath: '/basic-report/post-evaluation/$id/update'
      preLoaderRoute: typeof AuthBasicReportPostEvaluationIdUpdateRouteImport
      parentRoute: typeof AuthBasicReportPostEvaluationRouteRoute
    }
    '/_auth/basic-report/fixed-assets/$id/update': {
      id: '/_auth/basic-report/fixed-assets/$id/update'
      path: '/$id/update'
      fullPath: '/basic-report/fixed-assets/$id/update'
      preLoaderRoute: typeof AuthBasicReportFixedAssetsIdUpdateRouteImport
      parentRoute: typeof AuthBasicReportFixedAssetsRouteRoute
    }
    '/_auth/basic-report/equity-projects/$id/update': {
      id: '/_auth/basic-report/equity-projects/$id/update'
      path: '/$id/update'
      fullPath: '/basic-report/equity-projects/$id/update'
      preLoaderRoute: typeof AuthBasicReportEquityProjectsIdUpdateRouteImport
      parentRoute: typeof AuthBasicReportEquityProjectsRouteRoute
    }
    '/_auth/data-summary/completion-status/$quarter/$table': {
      id: '/_auth/data-summary/completion-status/$quarter/$table'
      path: '/$table'
      fullPath: '/data-summary/completion-status/$quarter/$table'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusQuarterTableRouteRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusQuarterRouteRoute
    }
    '/_auth/data-summary/investment-plan/table/$tableName/update': {
      id: '/_auth/data-summary/investment-plan/table/$tableName/update'
      path: '/$tableName/update'
      fullPath: '/data-summary/investment-plan/table/$tableName/update'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanTableTableNameUpdateRouteImport
      parentRoute: typeof AuthDataSummaryInvestmentPlanTableRoute
    }
    '/_auth/data-summary/investment-plan/table/$tableName/list': {
      id: '/_auth/data-summary/investment-plan/table/$tableName/list'
      path: '/$tableName/list'
      fullPath: '/data-summary/investment-plan/table/$tableName/list'
      preLoaderRoute: typeof AuthDataSummaryInvestmentPlanTableTableNameListRouteImport
      parentRoute: typeof AuthDataSummaryInvestmentPlanTableRoute
    }
    '/_auth/data-summary/completion-status/$quarter/$table/update': {
      id: '/_auth/data-summary/completion-status/$quarter/$table/update'
      path: '/update'
      fullPath: '/data-summary/completion-status/$quarter/$table/update'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusQuarterTableUpdateRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusQuarterTableRouteRoute
    }
    '/_auth/data-summary/completion-status/$quarter/$table/list': {
      id: '/_auth/data-summary/completion-status/$quarter/$table/list'
      path: '/list'
      fullPath: '/data-summary/completion-status/$quarter/$table/list'
      preLoaderRoute: typeof AuthDataSummaryCompletionStatusQuarterTableListRouteImport
      parentRoute: typeof AuthDataSummaryCompletionStatusQuarterTableRouteRoute
    }
  }
}

interface AuthBasicReportEquityProjectsRouteRouteChildren {
  AuthBasicReportEquityProjectsCreateRoute: typeof AuthBasicReportEquityProjectsCreateRoute
  AuthBasicReportEquityProjectsIndexRoute: typeof AuthBasicReportEquityProjectsIndexRoute
  AuthBasicReportEquityProjectsIdUpdateRoute: typeof AuthBasicReportEquityProjectsIdUpdateRoute
}

const AuthBasicReportEquityProjectsRouteRouteChildren: AuthBasicReportEquityProjectsRouteRouteChildren =
  {
    AuthBasicReportEquityProjectsCreateRoute:
      AuthBasicReportEquityProjectsCreateRoute,
    AuthBasicReportEquityProjectsIndexRoute:
      AuthBasicReportEquityProjectsIndexRoute,
    AuthBasicReportEquityProjectsIdUpdateRoute:
      AuthBasicReportEquityProjectsIdUpdateRoute,
  }

const AuthBasicReportEquityProjectsRouteRouteWithChildren =
  AuthBasicReportEquityProjectsRouteRoute._addFileChildren(
    AuthBasicReportEquityProjectsRouteRouteChildren,
  )

interface AuthBasicReportFixedAssetsRouteRouteChildren {
  AuthBasicReportFixedAssetsCreateRoute: typeof AuthBasicReportFixedAssetsCreateRoute
  AuthBasicReportFixedAssetsIndexRoute: typeof AuthBasicReportFixedAssetsIndexRoute
  AuthBasicReportFixedAssetsIdUpdateRoute: typeof AuthBasicReportFixedAssetsIdUpdateRoute
}

const AuthBasicReportFixedAssetsRouteRouteChildren: AuthBasicReportFixedAssetsRouteRouteChildren =
  {
    AuthBasicReportFixedAssetsCreateRoute:
      AuthBasicReportFixedAssetsCreateRoute,
    AuthBasicReportFixedAssetsIndexRoute: AuthBasicReportFixedAssetsIndexRoute,
    AuthBasicReportFixedAssetsIdUpdateRoute:
      AuthBasicReportFixedAssetsIdUpdateRoute,
  }

const AuthBasicReportFixedAssetsRouteRouteWithChildren =
  AuthBasicReportFixedAssetsRouteRoute._addFileChildren(
    AuthBasicReportFixedAssetsRouteRouteChildren,
  )

interface AuthBasicReportPostEvaluationRouteRouteChildren {
  AuthBasicReportPostEvaluationCreateRoute: typeof AuthBasicReportPostEvaluationCreateRoute
  AuthBasicReportPostEvaluationIndexRoute: typeof AuthBasicReportPostEvaluationIndexRoute
  AuthBasicReportPostEvaluationIdUpdateRoute: typeof AuthBasicReportPostEvaluationIdUpdateRoute
}

const AuthBasicReportPostEvaluationRouteRouteChildren: AuthBasicReportPostEvaluationRouteRouteChildren =
  {
    AuthBasicReportPostEvaluationCreateRoute:
      AuthBasicReportPostEvaluationCreateRoute,
    AuthBasicReportPostEvaluationIndexRoute:
      AuthBasicReportPostEvaluationIndexRoute,
    AuthBasicReportPostEvaluationIdUpdateRoute:
      AuthBasicReportPostEvaluationIdUpdateRoute,
  }

const AuthBasicReportPostEvaluationRouteRouteWithChildren =
  AuthBasicReportPostEvaluationRouteRoute._addFileChildren(
    AuthBasicReportPostEvaluationRouteRouteChildren,
  )

interface AuthBasicReportRouteRouteChildren {
  AuthBasicReportEquityProjectsRouteRoute: typeof AuthBasicReportEquityProjectsRouteRouteWithChildren
  AuthBasicReportFixedAssetsRouteRoute: typeof AuthBasicReportFixedAssetsRouteRouteWithChildren
  AuthBasicReportPostEvaluationRouteRoute: typeof AuthBasicReportPostEvaluationRouteRouteWithChildren
  AuthBasicReportIndexRoute: typeof AuthBasicReportIndexRoute
}

const AuthBasicReportRouteRouteChildren: AuthBasicReportRouteRouteChildren = {
  AuthBasicReportEquityProjectsRouteRoute:
    AuthBasicReportEquityProjectsRouteRouteWithChildren,
  AuthBasicReportFixedAssetsRouteRoute:
    AuthBasicReportFixedAssetsRouteRouteWithChildren,
  AuthBasicReportPostEvaluationRouteRoute:
    AuthBasicReportPostEvaluationRouteRouteWithChildren,
  AuthBasicReportIndexRoute: AuthBasicReportIndexRoute,
}

const AuthBasicReportRouteRouteWithChildren =
  AuthBasicReportRouteRoute._addFileChildren(AuthBasicReportRouteRouteChildren)

interface AuthDataSummaryCompletionStatusQuarterTableRouteRouteChildren {
  AuthDataSummaryCompletionStatusQuarterTableListRoute: typeof AuthDataSummaryCompletionStatusQuarterTableListRoute
  AuthDataSummaryCompletionStatusQuarterTableUpdateRoute: typeof AuthDataSummaryCompletionStatusQuarterTableUpdateRoute
}

const AuthDataSummaryCompletionStatusQuarterTableRouteRouteChildren: AuthDataSummaryCompletionStatusQuarterTableRouteRouteChildren =
  {
    AuthDataSummaryCompletionStatusQuarterTableListRoute:
      AuthDataSummaryCompletionStatusQuarterTableListRoute,
    AuthDataSummaryCompletionStatusQuarterTableUpdateRoute:
      AuthDataSummaryCompletionStatusQuarterTableUpdateRoute,
  }

const AuthDataSummaryCompletionStatusQuarterTableRouteRouteWithChildren =
  AuthDataSummaryCompletionStatusQuarterTableRouteRoute._addFileChildren(
    AuthDataSummaryCompletionStatusQuarterTableRouteRouteChildren,
  )

interface AuthDataSummaryCompletionStatusQuarterRouteRouteChildren {
  AuthDataSummaryCompletionStatusQuarterTableRouteRoute: typeof AuthDataSummaryCompletionStatusQuarterTableRouteRouteWithChildren
  AuthDataSummaryCompletionStatusQuarterIndexRoute: typeof AuthDataSummaryCompletionStatusQuarterIndexRoute
}

const AuthDataSummaryCompletionStatusQuarterRouteRouteChildren: AuthDataSummaryCompletionStatusQuarterRouteRouteChildren =
  {
    AuthDataSummaryCompletionStatusQuarterTableRouteRoute:
      AuthDataSummaryCompletionStatusQuarterTableRouteRouteWithChildren,
    AuthDataSummaryCompletionStatusQuarterIndexRoute:
      AuthDataSummaryCompletionStatusQuarterIndexRoute,
  }

const AuthDataSummaryCompletionStatusQuarterRouteRouteWithChildren =
  AuthDataSummaryCompletionStatusQuarterRouteRoute._addFileChildren(
    AuthDataSummaryCompletionStatusQuarterRouteRouteChildren,
  )

interface AuthDataSummaryCompletionStatusRouteRouteChildren {
  AuthDataSummaryCompletionStatusQuarterRouteRoute: typeof AuthDataSummaryCompletionStatusQuarterRouteRouteWithChildren
  AuthDataSummaryCompletionStatusIndexRoute: typeof AuthDataSummaryCompletionStatusIndexRoute
}

const AuthDataSummaryCompletionStatusRouteRouteChildren: AuthDataSummaryCompletionStatusRouteRouteChildren =
  {
    AuthDataSummaryCompletionStatusQuarterRouteRoute:
      AuthDataSummaryCompletionStatusQuarterRouteRouteWithChildren,
    AuthDataSummaryCompletionStatusIndexRoute:
      AuthDataSummaryCompletionStatusIndexRoute,
  }

const AuthDataSummaryCompletionStatusRouteRouteWithChildren =
  AuthDataSummaryCompletionStatusRouteRoute._addFileChildren(
    AuthDataSummaryCompletionStatusRouteRouteChildren,
  )

interface AuthDataSummaryInvestmentPlanTableRouteChildren {
  AuthDataSummaryInvestmentPlanTableTableNameListRoute: typeof AuthDataSummaryInvestmentPlanTableTableNameListRoute
  AuthDataSummaryInvestmentPlanTableTableNameUpdateRoute: typeof AuthDataSummaryInvestmentPlanTableTableNameUpdateRoute
}

const AuthDataSummaryInvestmentPlanTableRouteChildren: AuthDataSummaryInvestmentPlanTableRouteChildren =
  {
    AuthDataSummaryInvestmentPlanTableTableNameListRoute:
      AuthDataSummaryInvestmentPlanTableTableNameListRoute,
    AuthDataSummaryInvestmentPlanTableTableNameUpdateRoute:
      AuthDataSummaryInvestmentPlanTableTableNameUpdateRoute,
  }

const AuthDataSummaryInvestmentPlanTableRouteWithChildren =
  AuthDataSummaryInvestmentPlanTableRoute._addFileChildren(
    AuthDataSummaryInvestmentPlanTableRouteChildren,
  )

interface AuthDataSummaryInvestmentPlanRouteRouteChildren {
  AuthDataSummaryInvestmentPlanTableRoute: typeof AuthDataSummaryInvestmentPlanTableRouteWithChildren
  AuthDataSummaryInvestmentPlanIndexRoute: typeof AuthDataSummaryInvestmentPlanIndexRoute
}

const AuthDataSummaryInvestmentPlanRouteRouteChildren: AuthDataSummaryInvestmentPlanRouteRouteChildren =
  {
    AuthDataSummaryInvestmentPlanTableRoute:
      AuthDataSummaryInvestmentPlanTableRouteWithChildren,
    AuthDataSummaryInvestmentPlanIndexRoute:
      AuthDataSummaryInvestmentPlanIndexRoute,
  }

const AuthDataSummaryInvestmentPlanRouteRouteWithChildren =
  AuthDataSummaryInvestmentPlanRouteRoute._addFileChildren(
    AuthDataSummaryInvestmentPlanRouteRouteChildren,
  )

interface AuthDataSummaryMonthlyReportRouteRouteChildren {
  AuthDataSummaryMonthlyReportCreateRoute: typeof AuthDataSummaryMonthlyReportCreateRoute
  AuthDataSummaryMonthlyReportIndexRoute: typeof AuthDataSummaryMonthlyReportIndexRoute
  AuthDataSummaryMonthlyReportIdUpdateRoute: typeof AuthDataSummaryMonthlyReportIdUpdateRoute
}

const AuthDataSummaryMonthlyReportRouteRouteChildren: AuthDataSummaryMonthlyReportRouteRouteChildren =
  {
    AuthDataSummaryMonthlyReportCreateRoute:
      AuthDataSummaryMonthlyReportCreateRoute,
    AuthDataSummaryMonthlyReportIndexRoute:
      AuthDataSummaryMonthlyReportIndexRoute,
    AuthDataSummaryMonthlyReportIdUpdateRoute:
      AuthDataSummaryMonthlyReportIdUpdateRoute,
  }

const AuthDataSummaryMonthlyReportRouteRouteWithChildren =
  AuthDataSummaryMonthlyReportRouteRoute._addFileChildren(
    AuthDataSummaryMonthlyReportRouteRouteChildren,
  )

interface AuthDataSummaryRouteRouteChildren {
  AuthDataSummaryCompletionStatusRouteRoute: typeof AuthDataSummaryCompletionStatusRouteRouteWithChildren
  AuthDataSummaryInvestmentPlanRouteRoute: typeof AuthDataSummaryInvestmentPlanRouteRouteWithChildren
  AuthDataSummaryMonthlyReportRouteRoute: typeof AuthDataSummaryMonthlyReportRouteRouteWithChildren
  AuthDataSummaryIndexRoute: typeof AuthDataSummaryIndexRoute
}

const AuthDataSummaryRouteRouteChildren: AuthDataSummaryRouteRouteChildren = {
  AuthDataSummaryCompletionStatusRouteRoute:
    AuthDataSummaryCompletionStatusRouteRouteWithChildren,
  AuthDataSummaryInvestmentPlanRouteRoute:
    AuthDataSummaryInvestmentPlanRouteRouteWithChildren,
  AuthDataSummaryMonthlyReportRouteRoute:
    AuthDataSummaryMonthlyReportRouteRouteWithChildren,
  AuthDataSummaryIndexRoute: AuthDataSummaryIndexRoute,
}

const AuthDataSummaryRouteRouteWithChildren =
  AuthDataSummaryRouteRoute._addFileChildren(AuthDataSummaryRouteRouteChildren)

interface AuthRouteChildren {
  AuthBasicReportRouteRoute: typeof AuthBasicReportRouteRouteWithChildren
  AuthDataSummaryRouteRoute: typeof AuthDataSummaryRouteRouteWithChildren
  AuthIndexRoute: typeof AuthIndexRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthBasicReportRouteRoute: AuthBasicReportRouteRouteWithChildren,
  AuthDataSummaryRouteRoute: AuthDataSummaryRouteRouteWithChildren,
  AuthIndexRoute: AuthIndexRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  AuthenticationCallbackRoute: AuthenticationCallbackRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
