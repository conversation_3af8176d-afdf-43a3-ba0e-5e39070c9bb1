import { createFileRoute, Outlet, redirect } from '@tanstack/react-router'

export const Route = createFileRoute('/_auth/data-summary/investment-plan')({
  beforeLoad: ({ location }) => {
    // 如果直接访问子路由 `/table`，则重定向到父路由
    if (location.pathname.endsWith('/table')) {
      throw redirect({
        to: '/data-summary/investment-plan',
      })
    }
  },
  component: RouteComponent,

  loader: () => {
    return {
      name: '投资计划',
    }
  },
})

function RouteComponent() {
  return <Outlet />
}
