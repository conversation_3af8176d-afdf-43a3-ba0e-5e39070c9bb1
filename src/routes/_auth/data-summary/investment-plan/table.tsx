import { createFileRoute, Outlet } from '@tanstack/react-router'

import { InvestmentPlanTableNamesMap } from '@/pages/investment-plan/enum/InvestmentPlan'

export const Route = createFileRoute(
  '/_auth/data-summary/investment-plan/table',
)({
  component: RouteComponent,
  loader: ({ params }) => {
    const { tableName } = params as { tableName: string }
    const name =
      InvestmentPlanTableNamesMap[
        tableName as keyof typeof InvestmentPlanTableNamesMap
      ]

    return {
      name,
    }
  },
})

function RouteComponent() {
  return <Outlet />
}
