import { createFileRoute, Outlet } from '@tanstack/react-router'

import { CompletionStatusTableNamesMap } from '@/pages/completion-status/constants'

export const Route = createFileRoute(
  '/_auth/data-summary/completion-status/$quarter/$table',
)({
  component: RouteComponent,
  loader: ({ params }) => {
    const { table } = params as { table: string }
    const name =
      CompletionStatusTableNamesMap[
        table as keyof typeof CompletionStatusTableNamesMap
      ]

    return {
      name,
    }
  },
})

function RouteComponent() {
  return <Outlet />
}
