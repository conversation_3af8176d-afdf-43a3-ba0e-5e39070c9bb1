import { useParams } from '@tanstack/react-router'

import { CompletionStatusTableUpdateMap } from './constants'

export function TableUpdate() {
  const params = useParams({ strict: false })
  const table = params.table as keyof typeof CompletionStatusTableUpdateMap
  const TableComponent =
    table && table in CompletionStatusTableUpdateMap
      ? CompletionStatusTableUpdateMap[table]
      : null

  if (!TableComponent) {
    return null
  }

  return <TableComponent />
}
