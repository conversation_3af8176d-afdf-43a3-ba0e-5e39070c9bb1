import { useQuery, useMutation } from '@tanstack/react-query'
import { useParams, useSearch, Link } from '@tanstack/react-router'
import {
  Card,
  Input,
  message,
  Button,
  Table,
  type TableColumnsType,
  InputNumber,
} from 'antd'
import { createStyles } from 'antd-style'
import { RefreshCcw } from 'lucide-react'
import { useMemo, useState, useEffect, useCallback, memo } from 'react'

import { useAuth } from '@/contexts/auth.tsx'
import { extractYearAndQuarter } from '@/lib/dateUtils'
import { toNumber, addNumbers } from '@/lib/numberUtils.ts'
import { type APIResponse, request } from '@/lib/request.ts'

import { CompletionStatusTableNamesMap } from '../../constants.ts'

import {
  DATA_TYPE_INVESTMENT_PLAN,
  DISABLED_CELL_STYLE,
  DISABLED_COMMENCEMENT_CONTINUATION_STYLE,
} from './constants.ts'
import type {
  CalculableCompletionTotalField,
  CompletionTotalCompany,
  CompletionTotalItem,
} from './types.ts'

// 优化后的输入组件
const NumberInput = memo(
  ({
    value,
    record,
    field,
    disabled = false,
    onChange,
  }: {
    value: string | number
    record: CompletionTotalItem
    field: keyof CompletionTotalItem
    disabled?: boolean
    onChange: (
      record: CompletionTotalItem,
      field: string,
      value: number,
    ) => void
  }) => (
    <InputNumber
      value={toNumber(value)}
      onChange={(val) => val !== null && onChange(record, field, val)}
      disabled={DISABLED_CELL_STYLE.includes(record.style) || disabled}
      style={{ width: '100%' }}
      min={0}
      precision={2}
      controls={false}
    />
  ),
)

NumberInput.displayName = 'NumberInput'

const RemarkInput = memo(
  ({
    value,
    record,
    onChange,
  }: {
    value: string
    record: CompletionTotalItem
    onChange: (
      record: CompletionTotalItem,
      field: string,
      value: string,
    ) => void
  }) => (
    <Input
      value={value || ''}
      onChange={(e) => onChange(record, 'remarks', e.target.value)}
      placeholder="请输入备注信息"
    />
  ),
)

RemarkInput.displayName = 'RemarkInput'

const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customTable: css`
      ${antCls}-table-thead > tr > th {
        background-color: #e5ebfe;
        font-weight: 400;
      }
    `,
  }
})

export const CompletionTotalUpdate = () => {
  const { quarter = '' } = useParams({ strict: false })
  const { quarter: quarterNumber } = extractYearAndQuarter(quarter || '')
  const { styles } = useStyle()
  const [messageApi, contextHolder] = message.useMessage()
  const { company } = useAuth()
  const { id } = useSearch({ strict: false }) as { id: string }
  const { table } = useParams({ strict: false })

  const [tableData, setTableData] = useState<CompletionTotalItem[]>([])
  const [summaryData, setSummaryData] = useState<{
    id: string
    investment_year: string
    quarter: number
    company_id: string
    company_name: string
    consolidation: number
  }>({
    id: '',
    investment_year: '',
    company_id: '',
    company_name: '',
    consolidation: 1,
    quarter: quarterNumber,
  })

  // 刷新数据
  const { mutate, isPending } = useMutation({
    mutationFn: async (params: {
      company_id: string
      year: string
      quarter: number
    }) => {
      const res = await request<
        APIResponse<{
          data: CompletionTotalItem[]
        }>
      >('/completed-summary/investment-aggregate', {
        method: 'GET',
        query: params,
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        messageApi.success('刷新成功')
        const tableData = res.data?.data ?? []
        setTableData((prev) => {
          return tableData.map((v) => ({
            ...v,
            id: prev.find((item) => item.style === v.style)?.id ?? '',
          }))
        })
      } else {
        messageApi.error(res?.message)
      }
    },
    onError: (err) => messageApi.error(JSON.stringify(err)),
  })

  // 保存数据
  const { mutate: saveMutate, isPending: saveIsPending } = useMutation({
    mutationFn: async (params: CompletionTotalCompany) => {
      const res = await request<
        APIResponse<{
          data: CompletionTotalItem[]
        }>
      >('/completed-summary/investment-save', {
        method: 'POST',
        body: params,
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        messageApi.success('保存成功')
      } else {
        messageApi.error(res?.message)
      }
    },
    onError: (err) => messageApi.error(JSON.stringify(err)),
  })

  // 获取初始数据
  const { data, isLoading } = useQuery({
    queryKey: ['/completed-summary/investment-detail-by-id', id],
    queryFn: async ({ queryKey: [url, id] }) => {
      const response = await request<
        APIResponse<{
          data: CompletionTotalCompany
        }>
      >(url as string, { query: { id } })
      if (response.code !== 200001) return null
      return response?.data?.data ?? null
    },
    staleTime: 0,
    retry: false,
    enabled: !!id,
  })

  // 计算规则配置 - 集中定义所有计算依赖关系
  const calculationRules = useMemo(
    () => [
      // 规则1：自身total = 自身domestic + 自身overseas（如style=2/3/5-9）
      {
        targetStyles: [2, 3, 5, 6, 7, 8, 9],
        type: 'selfTotal' as const,
      },
      // 规则2：目标style的字段由依赖style的对应字段累加（如style=1依赖2/3）
      {
        targetStyle: 1,
        sourceStyles: [2, 3],
        fields: [
          'domestic',
          'overseas',
          'main',
          'non_main',
          'commencement',
          'funds_received',
          'continuation',
          'fund_source_self',
          'fund_source_loan',
          'fund_source_other',
        ] as CalculableCompletionTotalField[],
        type: 'sourceSum' as const,
      },
      // 规则3：style=4依赖5-9
      {
        targetStyle: 4,
        sourceStyles: [5, 6, 7, 8, 9],
        fields: [
          'domestic',
          'overseas',
          'main',
          'non_main',
          'fund_source_self',
          'fund_source_loan',
          'fund_source_other',
        ] as CalculableCompletionTotalField[],
        type: 'sourceSum' as const,
      },
      // 规则4：style=10依赖2-3和5-9
      {
        targetStyle: 10,
        sourceStyles: [2, 3, 5, 6, 7, 8, 9],
        fields: [
          'domestic',
          'overseas',
          'main',
          'non_main',
          'fund_source_self',
          'fund_source_loan',
          'fund_source_other',
        ] as CalculableCompletionTotalField[],
        type: 'sourceSum' as const,
      },
    ],
    [],
  )

  // 通用计算函数：根据配置规则计算目标字段
  const calculateByRule = useCallback(
    (
      data: CompletionTotalItem[],
      rule: (typeof calculationRules)[number],
    ): CompletionTotalItem[] => {
      const newData = [...data.map((item) => ({ ...item }))]

      if (rule.type === 'selfTotal') {
        // 处理自身total计算（如style=2/3/5-9）
        rule.targetStyles.forEach((style) => {
          const record = newData.find((item) => item.style === style)
          if (record) {
            record.total = addNumbers(record.domestic, record.overseas)
          }
        })
      }

      if (rule.type === 'sourceSum') {
        // 处理依赖其他记录的字段计算（如style=1/4/10）
        const targetRecord = newData.find(
          (item) => item.style === rule.targetStyle,
        )
        if (!targetRecord) return newData

        // 获取所有依赖的源记录
        const sourceRecords = rule.sourceStyles
          .map((style) => newData.find((item) => item.style === style))
          .filter(Boolean) as CompletionTotalItem[]

        // 1. 计算指定字段：累加源记录的对应字段
        rule.fields.forEach((field) => {
          targetRecord[field] = sourceRecords.reduce(
            (sum, item) => addNumbers(sum, item[field]),
            0,
          )
        })

        // 2. 计算total：累加源记录的domestic + overseas总和
        targetRecord.total = sourceRecords.reduce(
          (sum, item) => addNumbers(sum, item.domestic, item.overseas),
          0,
        )
      }

      return newData
    },
    [],
  )

  // 核心计算函数 - 按依赖顺序计算所有字段
  const calculateSums = useCallback(
    (data: CompletionTotalItem[]) => {
      // 复制原始数据
      let newData = [...data.map((item) => ({ ...item }))]

      // 按依赖顺序应用计算规则（先计算基础项，再计算汇总项）
      const ruleOrder = ['selfTotal', 'sourceSum']

      ruleOrder.forEach((ruleType) => {
        calculationRules
          .filter((rule) => rule.type === ruleType)
          .forEach((rule) => {
            newData = calculateByRule(newData, rule)
          })
      })

      return newData
    },
    [calculateByRule, calculationRules],
  )

  // 初始化表格数据
  useEffect(() => {
    if (data && data.CompletedSummaryInvestments?.length) {
      // 初始化时执行一次计算
      const initialData = calculateSums(data.CompletedSummaryInvestments)
      setTableData(initialData)
      setSummaryData({
        id: data.id || '',
        investment_year: data.investment_year,
        company_id: data.company_id,
        company_name: data.company_name,
        consolidation: data.consolidation,
        quarter: data.quarter,
      })
    }
  }, [data, calculateSums])

  const handleInputChange = useCallback(
    (record: CompletionTotalItem, field: string, value: number | string) => {
      setTableData((prevData) => {
        // 使用函数式更新
        const newData = prevData.map((item) =>
          item.style === record.style ? { ...item, [field]: value } : item,
        )
        return calculateSums(newData)
      })
    },
    [calculateSums],
  )

  // 处理表单提交
  const handleSubmit = useCallback(() => {
    const saveParams = {
      ...summaryData,
      CompletedSummaryInvestments: tableData,
    }
    saveMutate(saveParams)
  }, [tableData, summaryData, saveMutate])

  // 检查是否为所有报表（禁用编辑）
  const isConsolidatedReport = (
    consolidation: number | string | undefined,
  ): boolean => {
    return Number(consolidation) === 2
  }

  // 表格列配置
  const columns: TableColumnsType<CompletionTotalItem> = useMemo(() => {
    const isDisabled = isConsolidatedReport(summaryData.consolidation)
    return [
      {
        title: '栏目',
        dataIndex: 'style',
        key: 'style',
        width: 150,
        align: 'center',
        fixed: 'left',
        render: (value) =>
          DATA_TYPE_INVESTMENT_PLAN[
            value as keyof typeof DATA_TYPE_INVESTMENT_PLAN
          ] || '-',
      },
      {
        title: (
          <>
            <p>本企业本年度</p>
            <p>核准计划投资总额</p>
          </>
        ),
        dataIndex: 'plan_total',
        key: 'plan_total',
        align: 'center',
        fixed: 'left',
        minWidth: 150,
        render: (value, record) => (
          <NumberInput
            value={value}
            record={record}
            disabled={true}
            field="plan_total"
            onChange={handleInputChange}
          />
        ),
      },
      {
        title: (
          <>
            <p>本企业本年度</p>
            <p>计划投资总额</p>
          </>
        ),
        dataIndex: 'total',
        key: 'total',
        align: 'center',
        fixed: 'left',
        minWidth: 150,
        render: (value, record) => (
          <NumberInput
            value={value}
            record={record}
            disabled={true}
            field="total"
            onChange={handleInputChange}
          />
        ),
      },
      {
        title: '按投资地点划分',
        align: 'center',
        children: [
          {
            title: '境内',
            dataIndex: 'domestic',
            key: 'domestic',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="domestic"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
          {
            title: '境外',
            dataIndex: 'overseas',
            key: 'overseas',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="overseas"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
        ],
      },
      {
        title: '按投资方式划分',
        align: 'center',
        children: [
          {
            title: '主业',
            dataIndex: 'main',
            key: 'main',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="main"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
          {
            title: '非主业',
            dataIndex: 'non_main',
            key: 'non_main',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="non_main"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
        ],
      },
      {
        title: '按项目阶段划分',
        align: 'center',
        children: [
          {
            title: '新开工',
            dataIndex: 'commencement',
            key: 'commencement',
            minWidth: 150,
            align: 'center',
            render: (value, record) =>
              DISABLED_COMMENCEMENT_CONTINUATION_STYLE.includes(
                record.style,
              ) ? (
                <Input value="-" disabled />
              ) : (
                <NumberInput
                  value={value}
                  record={record}
                  field="commencement"
                  disabled={isDisabled}
                  onChange={handleInputChange}
                />
              ),
          },
          {
            title: '续建',
            dataIndex: 'continuation',
            key: 'continuation',
            minWidth: 150,
            align: 'center',
            render: (value, record) =>
              DISABLED_COMMENCEMENT_CONTINUATION_STYLE.includes(
                record.style,
              ) ? (
                <Input value="-" disabled />
              ) : (
                <NumberInput
                  value={value}
                  record={record}
                  field="continuation"
                  disabled={isDisabled}
                  onChange={handleInputChange}
                />
              ),
          },
        ],
      },
      {
        title: '到位资金',
        dataIndex: 'funds_received',
        key: 'funds_received',
        minWidth: 150,
        align: 'center',
        render: (value, record) =>
          DISABLED_COMMENCEMENT_CONTINUATION_STYLE.includes(record.style) ? (
            <Input value="-" disabled />
          ) : (
            <NumberInput
              value={value}
              record={record}
              field="funds_received"
              disabled={isDisabled}
              onChange={handleInputChange}
            />
          ),
      },
      {
        title: '按资金来源划分',
        align: 'center',
        children: [
          {
            title: '自有资金',
            dataIndex: 'fund_source_self',
            key: 'fund_source_self',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="fund_source_self"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
          {
            title: '贷款',
            dataIndex: 'fund_source_loan',
            key: 'fund_source_loan',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="fund_source_loan"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
          {
            title: '其他',
            dataIndex: 'fund_source_other',
            key: 'fund_source_other',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="fund_source_other"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
        ],
      },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        minWidth: 250,
        align: 'center',
        render: (value, record) => (
          <RemarkInput
            value={value || ''}
            record={record}
            onChange={handleInputChange}
          />
        ),
      },
    ]
  }, [handleInputChange, summaryData.consolidation])

  const title =
    CompletionStatusTableNamesMap[
      table as keyof typeof CompletionStatusTableNamesMap
    ]
  return (
    <div className="flex h-full flex-col">
      {contextHolder}

      <Card
        title={title}
        extra={
          isConsolidatedReport(summaryData.consolidation) ? null : (
            <Button
              color="primary"
              icon={<RefreshCcw className="mt-[4px] size-4" />}
              variant="text"
              onClick={() => {
                mutate({
                  company_id: company?.id || '',
                  year: summaryData?.investment_year || '',
                  quarter: summaryData?.quarter || 0,
                })
              }}
            >
              刷新数据
            </Button>
          )
        }
      >
        <div className="mb-4 flex items-center justify-between">
          <p>
            填报周期：
            {`${summaryData?.investment_year}-Q${summaryData?.quarter}`}
          </p>
          <p>编制单位：{summaryData?.company_name}</p>
          <p>金额：万元</p>
        </div>
        <Table
          size="small"
          tableLayout="auto"
          bordered
          loading={isLoading || isPending || saveIsPending}
          className={styles.customTable}
          dataSource={tableData}
          columns={columns}
          pagination={false}
          scroll={{ x: 'max-content' }}
          sticky={{ offsetHeader: 48 }}
          rowKey="style"
        />
        <div className="mt-4 flex justify-end gap-2">
          <Button type="primary" onClick={handleSubmit} loading={saveIsPending}>
            保存数据
          </Button>
          <Link
            to="/data-summary/completion-status/$quarter/$table/list"
            params={{ quarter: quarter || '', table: table || '' }}
          >
            <Button>返回</Button>
          </Link>
        </div>
      </Card>
    </div>
  )
}
