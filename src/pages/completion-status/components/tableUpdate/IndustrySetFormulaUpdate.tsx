import { useQuery, useMutation } from '@tanstack/react-query'
import { usePara<PERSON>, useSearch, Link } from '@tanstack/react-router'
import {
  Card,
  Input,
  message,
  Button,
  Table,
  type TableColumnsType,
  InputNumber,
} from 'antd'
import { createStyles } from 'antd-style'
import { RefreshCcw } from 'lucide-react'
import { useMemo, useState, useEffect, useCallback, memo } from 'react'

import { useAuth } from '@/contexts/auth.tsx'
import { extractYearAndQuarter } from '@/lib/dateUtils'
import { toNumber, addNumbers } from '@/lib/numberUtils.ts'
import { type APIResponse, request } from '@/lib/request.ts'
import { INDUSTRY_TYPE } from '@/universal/basic-form/constants.ts'

import { CompletionStatusTableNamesMap } from '../../constants.ts'

import { DISABLED_CELL_INDUSTRY_SET_FORMULA } from './constants.ts'
import type {
  CalculableIndustrySetFormulaField,
  IndustrySetFormulaCompany,
  IndustrySetFormula,
} from './types.ts'

// 行业键值对
const INDUSTRY_KEY_VALUE = [
  ...INDUSTRY_TYPE,
  { label: '合计', value: '0000' },
].reduce(
  (acc, item) => {
    acc[item.value] = item.label
    return acc
  },
  {} as Record<string, string>,
)

// 优化后的输入组件
const NumberInput = memo(
  ({
    value,
    record,
    field,
    disabled = false,
    onChange,
  }: {
    value: string | number
    record: IndustrySetFormula
    field: keyof IndustrySetFormula
    disabled?: boolean
    onChange: (record: IndustrySetFormula, field: string, value: number) => void
  }) => (
    <InputNumber
      value={toNumber(value)}
      onChange={(val) => val !== null && onChange(record, field, val)}
      disabled={
        DISABLED_CELL_INDUSTRY_SET_FORMULA.includes(record.style) || disabled
      }
      style={{ width: '100%' }}
      min={0}
      precision={2}
      controls={false}
    />
  ),
)

NumberInput.displayName = 'NumberInput'

const RemarkInput = memo(
  ({
    value,
    record,
    onChange,
  }: {
    value: string
    record: IndustrySetFormula
    onChange: (record: IndustrySetFormula, field: string, value: string) => void
  }) => (
    <Input
      value={value || ''}
      onChange={(e) => onChange(record, 'remarks', e.target.value)}
      placeholder="请输入备注信息"
    />
  ),
)

RemarkInput.displayName = 'RemarkInput'

const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customTable: css`
      ${antCls}-table-thead > tr > th {
        background-color: #e5ebfe;
        font-weight: 400;
      }
    `,
  }
})

export const IndustrySetFormulaUpdate = () => {
  const { quarter = '' } = useParams({ strict: false })
  const { quarter: quarterNumber } = extractYearAndQuarter(quarter || '')
  const { styles } = useStyle()
  const [messageApi, contextHolder] = message.useMessage()
  const { company } = useAuth()
  const { id } = useSearch({ strict: false }) as { id: string }
  const { table } = useParams({ strict: false })

  const [tableData, setTableData] = useState<IndustrySetFormula[]>([])
  const [summaryData, setSummaryData] = useState<{
    id: string
    investment_year: string
    quarter: number
    company_id: string
    company_name: string
    consolidation: number
  }>({
    id: '',
    investment_year: '',
    company_id: '',
    company_name: '',
    consolidation: 1,
    quarter: quarterNumber,
  })

  // 刷新数据
  const { mutate, isPending } = useMutation({
    mutationFn: async (params: {
      company_id: string
      year: string
      quarter: number
    }) => {
      const res = await request<
        APIResponse<{
          data: IndustrySetFormula[]
        }>
      >('/completed-summary/industry-aggregate', {
        method: 'GET',
        query: params,
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        messageApi.success('刷新成功')
        const tableData = res.data?.data ?? []
        setTableData((prev) => {
          return tableData.map((v) => ({
            ...v,
            id: prev.find((item) => item.style === v.style)?.id ?? '',
          }))
        })
      } else {
        messageApi.error(res?.message)
      }
    },
    onError: (err) => messageApi.error(JSON.stringify(err)),
  })

  // 保存数据
  const { mutate: saveMutate, isPending: saveIsPending } = useMutation({
    mutationFn: async (params: IndustrySetFormulaCompany) => {
      const res = await request<
        APIResponse<{
          data: IndustrySetFormula[]
        }>
      >('/completed-summary/industry-save', {
        method: 'POST',
        body: params,
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        messageApi.success('保存成功')
      } else {
        messageApi.error(res?.message)
      }
    },
    onError: (err) => messageApi.error(JSON.stringify(err)),
  })

  // 获取初始数据
  const { data, isLoading } = useQuery({
    queryKey: ['/completed-summary/industry-detail-by-id', id],
    queryFn: async ({ queryKey: [url, id] }) => {
      const response = await request<
        APIResponse<{
          data: IndustrySetFormulaCompany
        }>
      >(url as string, { query: { id } })
      if (response.code !== 200001) return null
      return response?.data?.data ?? null
    },
    staleTime: 0,
    retry: false,
    enabled: !!id,
  })

  // 计算规则配置 - 集中定义所有计算依赖关系
  const calculationRules = useMemo(
    () => [
      // 规则1：目标style的字段由依赖style的对应字段累加（如style0100=依赖0101/0199）
      {
        targetStyle: '0000',
        excludeStyles: ['0000'],
        fields: [
          'fixed_assets_domestic',
          'fixed_assets_overseas',
          'equity_investment_domestic',
          'equity_investment_overseas',
        ] as CalculableIndustrySetFormulaField[],
        type: 'sourceSum' as const,
      },
    ],
    [],
  )

  // 通用计算函数：根据配置规则计算目标字段
  const calculateByRule = useCallback(
    (
      data: IndustrySetFormula[],
      rule: (typeof calculationRules)[number],
    ): IndustrySetFormula[] => {
      const newData = [...data.map((item) => ({ ...item }))]

      if (rule.type === 'sourceSum') {
        const targetRecord = newData.find(
          (item) => item.style === rule.targetStyle,
        )
        if (!targetRecord) return newData

        // 获取所有依赖的源记录
        const sourceRecords = newData.filter(
          (item) => !rule.excludeStyles.includes(item.style),
        )

        // 1. 计算指定字段：累加源记录的对应字段
        rule.fields.forEach((field) => {
          targetRecord[field] = sourceRecords.reduce(
            (sum, item) => addNumbers(sum, item[field]),
            0,
          )
        })
      }

      return newData
    },
    [],
  )

  // 核心计算函数 - 按依赖顺序计算所有字段
  const calculateSums = useCallback(
    (data: IndustrySetFormula[]) => {
      let newData = [...data.map((item) => ({ ...item }))]
      calculationRules
        .filter((rule) => rule.type === 'sourceSum')
        .forEach((rule) => {
          newData = calculateByRule(newData, rule)
        })

      return newData
    },
    [calculateByRule, calculationRules],
  )

  // 初始化表格数据
  useEffect(() => {
    if (data && data.CompletedSummaryIndustrys?.length) {
      // 初始化时执行一次计算
      const initialData = calculateSums(data.CompletedSummaryIndustrys)
      setTableData(initialData)
      setSummaryData({
        id: data.id || '',
        investment_year: data.investment_year,
        company_id: data.company_id,
        company_name: data.company_name,
        consolidation: data.consolidation,
        quarter: data.quarter,
      })
    }
  }, [data, calculateSums])

  const handleInputChange = useCallback(
    (record: IndustrySetFormula, field: string, value: number | string) => {
      setTableData((prevData) => {
        // 使用函数式更新
        const newData = prevData.map((item) =>
          item.style === record.style ? { ...item, [field]: value } : item,
        )
        return calculateSums(newData)
      })
    },
    [calculateSums],
  )

  // 处理表单提交
  const handleSubmit = useCallback(() => {
    const saveParams = {
      ...summaryData,
      CompletedSummaryIndustrys: tableData,
    }
    saveMutate(saveParams)
  }, [tableData, summaryData, saveMutate])

  // 检查是否为所有报表（禁用编辑）
  const isConsolidatedReport = (
    consolidation: number | string | undefined,
  ): boolean => {
    return Number(consolidation) === 2
  }

  // 表格列配置
  const columns: TableColumnsType<IndustrySetFormula> = useMemo(() => {
    const isDisabled = isConsolidatedReport(summaryData.consolidation)
    return [
      {
        title: '类别名称',
        dataIndex: 'style',
        key: 'style',
        width: 150,
        align: 'center',
        fixed: 'left',
        render: (value) => INDUSTRY_KEY_VALUE[value] || '-',
      },
      {
        title: '代码',
        dataIndex: 'style',
        key: 'style',
        minWidth: 150,
        align: 'center',
        render: (value) => (value === '0000' ? '-' : value),
      },
      {
        title: '固定资产投资计划额',
        align: 'center',
        children: [
          {
            title: '境内',
            dataIndex: 'fixed_assets_domestic',
            key: 'fixed_assets_domestic',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="fixed_assets_domestic"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
          {
            title: '境外',
            dataIndex: 'fixed_assets_overseas',
            key: 'fixed_assets_overseas',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="fixed_assets_overseas"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
        ],
      },
      {
        title: '股权投资计划额',
        align: 'center',
        children: [
          {
            title: '境内',
            dataIndex: 'equity_investment_domestic',
            key: 'equity_investment_domestic',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="equity_investment_domestic"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
          {
            title: '境外',
            dataIndex: 'equity_investment_overseas',
            key: 'equity_investment_overseas',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="equity_investment_overseas"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
        ],
      },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        minWidth: 320,
        align: 'center',
        render: (value, record) => (
          <RemarkInput
            value={value || ''}
            record={record}
            onChange={handleInputChange}
          />
        ),
      },
    ]
  }, [handleInputChange, summaryData.consolidation])

  const title =
    CompletionStatusTableNamesMap[
      table as keyof typeof CompletionStatusTableNamesMap
    ]
  return (
    <div className="flex h-full flex-col">
      {contextHolder}

      <Card
        title={title}
        extra={
          isConsolidatedReport(summaryData.consolidation) ? null : (
            <Button
              color="primary"
              icon={<RefreshCcw className="mt-[4px] size-4" />}
              variant="text"
              onClick={() => {
                mutate({
                  company_id: company?.id || '',
                  year: summaryData?.investment_year || '',
                  quarter: summaryData?.quarter || 0,
                })
              }}
            >
              刷新数据
            </Button>
          )
        }
      >
        <div className="mb-4 flex items-center justify-between">
          <p>
            填报周期：
            {`${summaryData?.investment_year}-Q${summaryData?.quarter}`}
          </p>
          <p>编制单位：{summaryData?.company_name}</p>
          <p>金额：万元</p>
        </div>
        <Table
          size="small"
          tableLayout="auto"
          bordered
          loading={isLoading || isPending || saveIsPending}
          className={styles.customTable}
          dataSource={tableData}
          columns={columns}
          pagination={false}
          scroll={{ x: 'max-content' }}
          sticky={{ offsetHeader: 48 }}
          rowKey="style"
        />
        <div className="mt-4 flex justify-end gap-2">
          <Button type="primary" onClick={handleSubmit} loading={saveIsPending}>
            保存数据
          </Button>
          <Link
            to="/data-summary/completion-status/$quarter/$table/list"
            params={{ quarter: quarter || '', table: table || '' }}
          >
            <Button>返回</Button>
          </Link>
        </div>
      </Card>
    </div>
  )
}
