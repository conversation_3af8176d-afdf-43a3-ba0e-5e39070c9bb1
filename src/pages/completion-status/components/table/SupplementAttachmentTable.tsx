import { useQuery, useMutation } from '@tanstack/react-query'
import { useRouter, useParams } from '@tanstack/react-router'
import {
  Button,
  Card,
  DatePicker,
  Form,
  Table,
  TreeSelect,
  Divider,
  Popconfirm,
  message,
  type TableColumnsType,
  Typography,
} from 'antd'
import { createStyles } from 'antd-style'
import dayjs from 'dayjs'
import {
  CalendarClockIcon,
  BadgeJapaneseYenIcon,
  FilterIcon,
} from 'lucide-react'
import numeral from 'numeral'
import {
  parseAsBoolean,
  parseAsInteger,
  parseAsString,
  useQueryStates,
} from 'nuqs'
import { useMemo, useCallback, useEffect } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { useAuth } from '@/contexts/auth.tsx'
import { useCompanyOptions } from '@/hook/useCompanies'
import { extractYearAndQuarter } from '@/lib/dateUtils'
import { type APIResponse, request } from '@/lib/request'

import {
  CompletionStatusTableNamesMap,
  CompletionStatusTable,
} from '../../constants'

import type { FixedAssetProjectPageDTO } from './types'

interface SearchFormValues {
  company_id?: string // 填报单位
  date?: [dayjs.Dayjs | null, dayjs.Dayjs | null] // 日期范围
}

// 修改tabs样式
const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customCard: css`
      ${antCls}-card-body {
        padding-top: 0;
      }
    `,
  }
})

export function SupplementAttachmentTable() {
  return <>补充附件</>
}

export function SupplementAttachmentTableNew() {
  const { quarter = '' } = useParams({ strict: false })
  const { year, quarter: quarterNumber } = extractYearAndQuarter(quarter || '')
  const { styles } = useStyle()
  const { table } = useParams({ strict: false })
  const { company } = useAuth()
  const [form] = Form.useForm()
  const [filters, setFilters] = useQueryStates({
    page_num: parseAsInteger.withDefault(1),
    page_size: parseAsInteger.withDefault(10),
    use_total: parseAsBoolean.withDefault(true),
    company_id: parseAsString.withDefault(''),
    start_time: parseAsString.withDefault(''),
    end_time: parseAsString.withDefault(''),
  })

  const { options: companyOptions, isLoading: isLoadingCompanies } =
    useCompanyOptions()

  const formInitialValues = useMemo<SearchFormValues>(
    () => ({
      company_id: filters.company_id || '',
      date: [
        filters.start_time ? dayjs(filters.start_time) : null,
        filters.end_time ? dayjs(filters.end_time) : null,
      ],
    }),
    [filters.company_id, filters.start_time, filters.end_time],
  )

  useEffect(() => {
    form.setFieldsValue(formInitialValues)
  }, [form, formInitialValues])

  const { data: statisticalData } = useQuery({
    queryKey: [
      '/completed-summary/fixed-assets-summary',
      filters.company_id,
      year,
      quarterNumber,
    ] as const,
    queryFn: async ({ queryKey: [url, company_id, year, quarterNumber] }) => {
      const response = await request<APIResponse<FixedAssetProjectPageDTO>>(
        url as string,
        {
          query: {
            company_id,
            year,
            quarter: quarterNumber,
          },
        },
      )
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
  })

  const { data, isLoading, isFetching, refetch } = useQuery({
    queryKey: [
      '/completed-summary/fixed-assets-list',
      filters,
      year,
      quarterNumber,
    ] as const,
    queryFn: async ({ queryKey: [url, filters, year, quarterNumber] }) => {
      const response = await request<
        APIResponse<{
          list: FixedAssetProjectPageDTO[]
          total: number
        }>
      >(url as string, {
        query: {
          ...filters,

          year,
          quarter: quarterNumber,
        },
      })
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
  })

  const handleDelete = useMutation({
    mutationFn: async (id: string | undefined) => {
      const res = await request<APIResponse<null>>('/monthly-report', {
        method: 'DELETE',
        body: {
          monthly_ids: id,
        },
      })
      if (res.code === 200001) {
        message.success('操作成功')
        refetch()
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const columns: TableColumnsType<FixedAssetProjectPageDTO> = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '附件名称',
        dataIndex: 'company_name',
        minWidth: 300,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },

      {
        title: '操作',
        width: 200,
        fixed: 'right',
        render: (_, record) => {
          return (
            <>
              <Button type="link" size="small">
                预览
              </Button>
              <Divider type="vertical" />
              <Button type="link" size="small">
                下载
              </Button>
              <Divider type="vertical" />
              <Popconfirm
                title="确认删除？"
                okText="确认"
                cancelText="取消"
                onConfirm={() => handleDelete.mutate(record.id)}
              >
                <Button type="link" danger size="small">
                  删除
                </Button>
              </Popconfirm>
            </>
          )
        },
      },
    ]
  }, [handleDelete])

  const onSearch = useCallback(
    (values: SearchFormValues) => {
      setFilters((prev) => ({
        ...prev,
        page_num: 1,
        start_time: values.date?.[0]?.format('YYYY-MM-DD') || '',
        end_time: values.date?.[1]?.format('YYYY-MM-DD') || '',
        company_id: values.company_id || '',
      }))
    },
    [setFilters],
  )

  const onReset = useCallback(() => {
    form.resetFields()
    setFilters((prev) => ({
      ...prev,
      page_num: 1,
      company_id: '',
      start_time: '',
      end_time: '',
      sort_field: '',
      sort_order: '',
    }))
  }, [form, setFilters])

  const router = useRouter()

  return (
    <div className="flex h-full flex-col gap-4">
      <Button
        className="-my-4 self-start"
        type="link"
        onClick={() => router.history.back()}
      >
        {'<< 返回上一页'}
      </Button>
      <Card className={styles.customCard}>
        <div className="space-y-6">
          <h2 className="mt-3 text-xl font-semibold">{company?.name}</h2>
          <div className="flex items-center gap-16">
            <div className="flex items-center gap-2">
              <CalendarClockIcon className="size-4" />
              <span className="text-sm text-[#666]">固定资产投资计划额:</span>
              <span className="text-xl font-semibold">
                {numeral(statisticalData?.total).format('0,0.00')}万元
              </span>
            </div>
            <div className="flex items-center gap-2">
              <BadgeJapaneseYenIcon className="size-4" />
              <span className="text-sm text-[#666]">股权投资计划额:</span>
              <span className="text-xl font-semibold">
                {numeral(statisticalData?.total).format('0,0.00')}万元
              </span>
            </div>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-col gap-4">
          <Form form={form} onFinish={onSearch} onReset={onReset}>
            <div className="flex items-end gap-2">
              <div className="grid flex-1 grid-cols-3 gap-4 2xl:grid-cols-4">
                <Form.Item className="!mb-0" name="company_id">
                  <TreeSelect
                    showSearch
                    placeholder="请选择填报单位"
                    allowClear
                    treeDefaultExpandAll
                    loading={isLoadingCompanies}
                    treeData={companyOptions}
                    fieldNames={{
                      label: 'name',
                      value: 'id',
                    }}
                    prefix={<FormItemPrefix title="填报单位" />}
                  />
                </Form.Item>

                <Form.Item className="!mb-0" name="date">
                  <DatePicker.RangePicker
                    className="w-full"
                    prefix={<FormItemPrefix title="创建时间" />}
                  />
                </Form.Item>
              </div>
              <div className="flex shrink-0 grow-0 items-center gap-2">
                <Button
                  type="default"
                  icon={<FilterIcon className="size-3.5" />}
                />
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
                <Button type="text" htmlType="reset">
                  清空
                </Button>
              </div>
            </div>
          </Form>
          <div className="flex items-center gap-2 text-[16px] font-[600]">
            {
              CompletionStatusTableNamesMap[
                table as (typeof CompletionStatusTable)[keyof typeof CompletionStatusTable]
              ]
            }
          </div>
          <SkeletonTable
            loading={isLoading || isFetching}
            columns={columns as SkeletonTableColumnsType[]}
          >
            <Table
              size="small"
              rowSelection={{
                type: 'checkbox',
                columnWidth: 40,
                align: 'center',
              }}
              tableLayout="auto"
              dataSource={data?.list ?? []}
              columns={columns}
              scroll={{ x: 'max-content' }}
              sticky={{ offsetHeader: 48 }}
              pagination={{
                showQuickJumper: true,
                showSizeChanger: true,
                total: data?.total,
              }}
              rowKey="id"
            />
          </SkeletonTable>
        </div>
      </Card>
    </div>
  )
}
