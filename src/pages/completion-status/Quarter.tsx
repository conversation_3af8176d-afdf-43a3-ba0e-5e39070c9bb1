import { useQuery } from '@tanstack/react-query'
import { Link, useParams, useRouter } from '@tanstack/react-router'
import { Button, Card, Table, type TableColumnsType } from 'antd'
import { CalendarClockIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo } from 'react'

import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { useAuth } from '@/contexts/auth'
import { extractYearAndQuarter } from '@/lib/dateUtils'
import { type APIResponse, request } from '@/lib/request'
import { COMPANY_LEVEL } from '@/universal/data-summary/constants.ts'

import type { CompletionTotalPageDTO } from './components/table/types'
import { CompletionStatusTableNamesMap } from './constants'

export function QuarterIndexPage() {
  const router = useRouter()
  const { company } = useAuth()

  const { quarter } = useParams({ strict: false })
  const { year, quarter: quarterNumber } = extractYearAndQuarter(quarter || '')

  const { data: statisticalData } = useQuery({
    queryKey: [
      '/completed-summary/investment-summary',
      company?.id,
      year,
      quarterNumber,
    ] as const,
    queryFn: async ({ queryKey: [url, company_id, year, quarterNumber] }) => {
      const response = await request<APIResponse<CompletionTotalPageDTO>>(
        url as string,
        {
          query: {
            company_id,
            consolidation: 1, //汇总
            year: year,
            quarter: quarterNumber,
          },
        },
      )
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: !!company?.id,
  })

  const tableData = useMemo(() => {
    return Object.entries(CompletionStatusTableNamesMap).map(
      ([key, value]) => ({
        label: value,
        value: key,
      }),
    )
  }, [])

  const columns = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '表单名称',
        dataIndex: 'label',
        minWidth: 160,
        render: (label, record) => (
          <Link
            to="/data-summary/completion-status/$quarter/$table/list"
            params={{ quarter: quarter || '', table: record.value }}
          >
            {label}
          </Link>
        ),
      },
    ] as TableColumnsType<(typeof tableData)[number]>
  }, [quarter])

  return (
    <div className="flex h-full flex-col gap-4">
      <Button
        className="-my-4 self-start"
        type="link"
        onClick={() => router.history.back()}
      >
        {'<< 返回上一页'}
      </Button>
      <Card>
        <div className="space-y-6">
          <div className="flex justify-between">
            <h2 className="text-xl font-semibold">{company?.name}</h2>
            <h3 className="text-lg font-semibold">
              {year}年第{quarterNumber}季度
            </h3>
          </div>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度完成投资总额：</span>
            <span className="text-xl font-semibold">
              {numeral(statisticalData?.total).format('0,0.00')}万元
            </span>
          </div>
        </div>
      </Card>
      <Card
        title="投资计划表"
        extra={
          <div className="flex items-center gap-2">
            {company?.level === COMPANY_LEVEL.GROUP && (
              <Button type="primary">上报国资委</Button>
            )}

            <Button>导出数据</Button>
          </div>
        }
      >
        <SkeletonTable columns={columns as SkeletonTableColumnsType[]}>
          <Table
            rowKey="value"
            size="small"
            pagination={false}
            dataSource={tableData}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
          />
        </SkeletonTable>
      </Card>
    </div>
  )
}
