import { useParams } from '@tanstack/react-router'

import { CompletionStatusTableMap } from './constants'

export function TableList() {
  const params = useParams({ strict: false })
  const table = params.table as keyof typeof CompletionStatusTableMap
  const TableComponent =
    table && table in CompletionStatusTableMap
      ? CompletionStatusTableMap[table]
      : null

  if (!TableComponent) {
    return null
  }

  return <TableComponent />
}
