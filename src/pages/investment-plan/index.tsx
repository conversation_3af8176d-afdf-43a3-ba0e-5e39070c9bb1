import { useQuery } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'
import { Button, Card, Table, type TableColumnsType } from 'antd'
import { CalendarClockIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo } from 'react'

import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { useApp } from '@/contexts/app.tsx'
import { useAuth } from '@/contexts/auth'
import { type APIResponse, request } from '@/lib/request'
import { type InvestmentPlanPageDTO } from '@/pages/investment-plan/components/table/types.ts'
import { COMPANY_LEVEL } from '@/universal/data-summary/constants.ts'

import { InvestmentPlanTableNamesMap } from './enum/InvestmentPlan.ts'

export function InvestmentPlanIndexPage() {
  const { company } = useAuth()
  const { date } = useApp()
  const { data: statisticalData } = useQuery({
    queryKey: ['/plan-summary/investment-summary', date] as const,
    queryFn: async ({ queryKey: [url, date] }) => {
      const response = await request<APIResponse<InvestmentPlanPageDTO>>(
        url as string,
        {
          query: {
            consolidation: 1, //汇总数据
            year: date.year(),
          },
        },
      )
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
  })

  const tableData = useMemo(() => {
    return Object.entries(InvestmentPlanTableNamesMap).map(([key, value]) => ({
      label: value,
      value: key,
    }))
  }, [])

  const columns = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '表单名称',
        dataIndex: 'label',
        minWidth: 160,
        render: (label, record) => (
          <Link
            to="/data-summary/investment-plan/table/$tableName/list"
            params={{ tableName: record.value }}
          >
            {label}
          </Link>
        ),
      },
    ] as TableColumnsType<(typeof tableData)[number]>
  }, [])

  return (
    <div className="flex h-full flex-col gap-4">
      <Card>
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">{company?.name}</h2>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度计划投资总额：</span>
            <span className="text-xl font-semibold">
              {numeral(statisticalData?.total).format('0,0.00')}万元
            </span>
          </div>
        </div>
      </Card>
      <Card
        title="投资计划表"
        extra={
          <div className="flex items-center gap-2">
            {company?.level === COMPANY_LEVEL.GROUP && (
              <>
                <Button type="primary">上报国资委</Button>
                <Button>设置填报截止时间</Button>
              </>
            )}

            <Button>导出数据</Button>
          </div>
        }
      >
        <SkeletonTable columns={columns as SkeletonTableColumnsType[]}>
          <Table
            rowKey="value"
            size="small"
            pagination={false}
            dataSource={tableData}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
          />
        </SkeletonTable>
      </Card>
    </div>
  )
}
