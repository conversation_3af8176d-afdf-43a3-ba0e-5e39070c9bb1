/**
 * 计划总表
 */
export interface InvestmentPlanPageDTO {
  id: string
  plan_summary_investment_id: string
  investment_year: string
  company_id: string
  company_name: string
  consolidation: number
  style: number
  total: number
  domestic: number
  overseas: number
  main: number
  non_main: number
  commencement: number
  continuation: number
  fund_source_self: number
  fund_source_loan: number
  fund_source_other: number
  remarks: string
  created_at: string
  updated_at: string
  deleted_at: string | null
  creator_id: string
  creator_name: string
}

/**
 * 战新表
 */
export interface StrategicNewPageDTO {
  id: string
  investment_year: string
  company_id: string
  company_name: string
  consolidation: number
  style: number
  fixed_assets_domestic: number
  fixed_assets_overseas: number
  equity_investment_domestic: number
  equity_investment_overseas: number
  cross_border_domestic: number
  cross_border_overseas: number
  remarks: string
  created_at: string
  updated_at: string
}

/**
 * 行业设公式表
 */
export interface IndustrySetFormulaPageDTO {
  id: string
  investment_year: string
  company_id: string
  company_name: string
  consolidation: number
  style: number
  fixed_assets_domestic: number
  fixed_assets_overseas: number
  equity_investment_domestic: number
  equity_investment_overseas: number
  remarks: string
  created_at: string
}

/**
 * 区域表
 */
export interface AreaPageDTO {
  id: string
  investment_year: string
  company_id: string
  company_name: string
  consolidation: number
  style: number
  fixed_assets: number
  equity_investment: number
  remarks: string
  created_at: string
}

/**
 * 非主业投资表
 */
export interface NonMainBusinessPlanPageDTO {
  id: string
  investment_year: string
  company_id: string
  company_name: string
  consolidation: number
  total: number
  non_main_total: number
  special1: number
  special2: number
  special3: number
  special4: number
  special_total: number
  excluded_non_main: number
  apply_non_main: number
  remarks: string
}

/**
 * 固资项目表
 */
export interface FixedAssetProjectPageDTO {
  id: string
  investment_year: string
  company_id: string
  company_name: string
  consolidation: number
  total: number
  count: number
}

/**
 * 股权项目表
 */
export interface EquityProjectPageDTO {
  id: string
  investment_year: string
  company_id: string
  company_name: string
  consolidation: number
  total: number
  count: number
}

/** 后评价表 */
export interface PostEvaluationPageDTO {
  id: string
  investment_year: string
  company_id: string
  company_name: string
  consolidation: number
  total: number
  count: number
}

/** 非主业投资项目计划表 */
export interface NonMainBusinessInvestmentProjectPlanPageDTO {
  id: string
  investment_year: string
  company_id: string
  company_name: string
  consolidation: number
  total: number
  count: number
}
