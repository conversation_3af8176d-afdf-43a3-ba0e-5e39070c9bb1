import { useQuery, useMutation } from '@tanstack/react-query'
import { useParams, useSearch, Link } from '@tanstack/react-router'
import {
  Card,
  Input,
  message,
  Button,
  Table,
  type TableColumnsType,
  InputNumber,
} from 'antd'
import { createStyles } from 'antd-style'
import { RefreshCcw } from 'lucide-react'
import { useMemo, useState, useEffect, useCallback, memo } from 'react'

import { useAuth } from '@/contexts/auth.tsx'
import { toNumber, addNumbers } from '@/lib/numberUtils.ts'
import { type APIResponse, request } from '@/lib/request.ts'

import { InvestmentPlanTableNamesMap } from '../../enum/InvestmentPlan.ts'

import {
  DATA_TYPE_STRATEGIC_NEW,
  DISABLED_CELL_STRATEGIC_NEW,
} from './constants.ts'
import type {
  CalculableStrategicNewField,
  StrategicNewCompany,
  StrategicNew,
} from './types.ts'

// 优化后的输入组件
const NumberInput = memo(
  ({
    value,
    record,
    field,
    disabled = false,
    onChange,
  }: {
    value: string | number
    record: StrategicNew
    field: keyof StrategicNew
    disabled?: boolean
    onChange: (record: StrategicNew, field: string, value: number) => void
  }) => (
    <InputNumber
      value={toNumber(value)}
      onChange={(val) => val !== null && onChange(record, field, val)}
      disabled={DISABLED_CELL_STRATEGIC_NEW.includes(record.style) || disabled}
      style={{ width: '100%' }}
      min={0}
      precision={2}
      controls={false}
    />
  ),
)

NumberInput.displayName = 'NumberInput'

const RemarkInput = memo(
  ({
    value,
    record,
    onChange,
  }: {
    value: string
    record: StrategicNew
    onChange: (record: StrategicNew, field: string, value: string) => void
  }) => (
    <Input
      value={value || ''}
      onChange={(e) => onChange(record, 'remarks', e.target.value)}
      placeholder="请输入备注信息"
    />
  ),
)

RemarkInput.displayName = 'RemarkInput'

const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customTable: css`
      ${antCls}-table-thead > tr > th {
        background-color: #e5ebfe;
        font-weight: 400;
      }
    `,
  }
})

export const StrategicNewUpdate = () => {
  const { styles } = useStyle()
  const [messageApi, contextHolder] = message.useMessage()
  const { company } = useAuth()
  const { id } = useSearch({ strict: false }) as { id: string }
  const { tableName } = useParams({ strict: false })

  const [tableData, setTableData] = useState<StrategicNew[]>([])
  const [summaryData, setSummaryData] = useState<{
    id: string
    investment_year: string
    company_id: string
    company_name: string
    consolidation: number
  }>({
    id: '',
    investment_year: '',
    company_id: '',
    company_name: '',
    consolidation: 1,
  })

  // 刷新数据
  const { mutate, isPending } = useMutation({
    mutationFn: async (params: { company_id: string; year: string }) => {
      const res = await request<
        APIResponse<{
          data: StrategicNew[]
        }>
      >('/plan-summary/industry-new-aggregate', {
        method: 'GET',
        query: params,
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        messageApi.success('刷新成功')
        const tableData = res.data?.data ?? []
        setTableData((prev) => {
          return tableData.map((v) => ({
            ...v,
            id: prev.find((item) => item.style === v.style)?.id ?? '',
          }))
        })
      } else {
        messageApi.error(res?.message)
      }
    },
    onError: (err) => messageApi.error(JSON.stringify(err)),
  })

  // 保存数据
  const { mutate: saveMutate, isPending: saveIsPending } = useMutation({
    mutationFn: async (params: StrategicNewCompany) => {
      const res = await request<
        APIResponse<{
          data: StrategicNew[]
        }>
      >('/plan-summary/industry-new-save', {
        method: 'POST',
        body: params,
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        messageApi.success('保存成功')
      } else {
        messageApi.error(res?.message)
      }
    },
    onError: (err) => messageApi.error(JSON.stringify(err)),
  })

  // 获取初始数据
  const { data, isLoading } = useQuery({
    queryKey: ['/plan-summary/industry-new-detail-by-id', id],
    queryFn: async ({ queryKey: [url, id] }) => {
      const response = await request<
        APIResponse<{
          data: StrategicNewCompany
        }>
      >(url as string, { query: { id } })
      if (response.code !== 200001) return null
      return response?.data?.data ?? null
    },
    staleTime: 0,
    retry: false,
    enabled: !!id,
  })

  // 计算规则配置 - 集中定义所有计算依赖关系
  const calculationRules = useMemo(
    () => [
      // 规则1：目标style的字段由依赖style的对应字段累加（如style0100=依赖0101/0199）
      {
        targetStyle: '0100',
        sourceStyles: [
          '0101',
          '0102',
          '0103',
          '0104',
          '0105',
          '0106',
          '0107',
          '0108',
          '0199',
        ],
        fields: [
          'fixed_assets_domestic',
          'fixed_assets_overseas',
          'equity_investment_domestic',
          'equity_investment_overseas',
          'cross_border_domestic',
          'cross_border_overseas',
        ] as CalculableStrategicNewField[],
        type: 'sourceSum' as const,
      },
      // 规则2：style=0200依赖0201-0299
      {
        targetStyle: '0200',
        sourceStyles: ['0201', '0299'],
        fields: [
          'fixed_assets_domestic',
          'fixed_assets_overseas',
          'equity_investment_domestic',
          'equity_investment_overseas',
          'cross_border_domestic',
          'cross_border_overseas',
        ] as CalculableStrategicNewField[],
        type: 'sourceSum' as const,
      },
      // 规则3：style=0300依赖0300-0399
      {
        targetStyle: '0300',
        sourceStyles: ['0301', '0302', '0303', '0304', '0305', '0399'],
        fields: [
          'fixed_assets_domestic',
          'fixed_assets_overseas',
          'equity_investment_domestic',
          'equity_investment_overseas',
          'cross_border_domestic',
          'cross_border_overseas',
        ] as CalculableStrategicNewField[],
        type: 'sourceSum' as const,
      },
      // 规则4：style=0400依赖0400-0499
      {
        targetStyle: '0400',
        sourceStyles: ['0401', '0402', '0499'],
        fields: [
          'fixed_assets_domestic',
          'fixed_assets_overseas',
          'equity_investment_domestic',
          'equity_investment_overseas',
          'cross_border_domestic',
          'cross_border_overseas',
        ] as CalculableStrategicNewField[],
        type: 'sourceSum' as const,
      },
      // 规则5：style=0600依赖0600-0499
      {
        targetStyle: '0600',
        sourceStyles: [
          '0601',
          '0602',
          '0603',
          '0604',
          '0605',
          '0606',
          '0607',
          '0699',
        ],
        fields: [
          'fixed_assets_domestic',
          'fixed_assets_overseas',
          'equity_investment_domestic',
          'equity_investment_overseas',
          'cross_border_domestic',
          'cross_border_overseas',
        ] as CalculableStrategicNewField[],
        type: 'sourceSum' as const,
      },
      // 合计
      {
        targetStyle: '0000',
        sourceStyles: [
          '0100',
          '0200',
          '0300',
          '0400',
          '0500',
          '0600',
          '0700',
          '0800',
          '0900',
        ],
        fields: [
          'fixed_assets_domestic',
          'fixed_assets_overseas',
          'equity_investment_domestic',
          'equity_investment_overseas',
          'cross_border_domestic',
          'cross_border_overseas',
        ] as CalculableStrategicNewField[],
        type: 'sourceSum' as const,
      },
    ],
    [],
  )

  // 通用计算函数：根据配置规则计算目标字段
  const calculateByRule = useCallback(
    (
      data: StrategicNew[],
      rule: (typeof calculationRules)[number],
    ): StrategicNew[] => {
      const newData = [...data.map((item) => ({ ...item }))]

      if (rule.type === 'sourceSum') {
        const targetRecord = newData.find(
          (item) => item.style === rule.targetStyle,
        )
        if (!targetRecord) return newData

        // 获取所有依赖的源记录
        const sourceRecords = rule.sourceStyles
          .map((style) => newData.find((item) => item.style === style))
          .filter(Boolean) as StrategicNew[]

        // 1. 计算指定字段：累加源记录的对应字段
        rule.fields.forEach((field) => {
          targetRecord[field] = sourceRecords.reduce(
            (sum, item) => addNumbers(sum, item[field]),
            0,
          )
        })
      }

      return newData
    },
    [],
  )

  // 核心计算函数 - 按依赖顺序计算所有字段
  const calculateSums = useCallback(
    (data: StrategicNew[]) => {
      let newData = [...data.map((item) => ({ ...item }))]
      calculationRules
        .filter((rule) => rule.type === 'sourceSum')
        .forEach((rule) => {
          newData = calculateByRule(newData, rule)
        })

      return newData
    },
    [calculateByRule, calculationRules],
  )

  // 初始化表格数据
  useEffect(() => {
    if (data && data.plan_summary_industry_news?.length) {
      // 初始化时执行一次计算
      const initialData = calculateSums(data.plan_summary_industry_news)
      setTableData(initialData)
      setSummaryData({
        id: data.id || '',
        investment_year: data.investment_year,
        company_id: data.company_id,
        company_name: data.company_name,
        consolidation: data.consolidation,
      })
    }
  }, [data, calculateSums])

  const handleInputChange = useCallback(
    (record: StrategicNew, field: string, value: number | string) => {
      setTableData((prevData) => {
        // 使用函数式更新
        const newData = prevData.map((item) =>
          item.style === record.style ? { ...item, [field]: value } : item,
        )
        return calculateSums(newData)
      })
    },
    [calculateSums],
  )

  // 处理表单提交
  const handleSubmit = useCallback(() => {
    const saveParams = {
      ...summaryData,
      plan_summary_industry_news: tableData,
    }
    saveMutate(saveParams)
  }, [tableData, summaryData, saveMutate])

  // 检查是否为所有报表（禁用编辑）
  const isConsolidatedReport = (
    consolidation: number | string | undefined,
  ): boolean => {
    return Number(consolidation) === 2
  }

  // 表格列配置
  const columns: TableColumnsType<StrategicNew> = useMemo(() => {
    const isDisabled = isConsolidatedReport(summaryData.consolidation)
    return [
      {
        title: '类别名称',
        dataIndex: 'style',
        key: 'style',
        width: 150,
        align: 'center',
        fixed: 'left',
        render: (value) =>
          DATA_TYPE_STRATEGIC_NEW[
            value as keyof typeof DATA_TYPE_STRATEGIC_NEW
          ] || '-',
      },
      {
        title: '固定资产投资计划额',
        align: 'center',
        children: [
          {
            title: '境内',
            dataIndex: 'fixed_assets_domestic',
            key: 'fixed_assets_domestic',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="fixed_assets_domestic"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
          {
            title: '境外',
            dataIndex: 'fixed_assets_overseas',
            key: 'fixed_assets_overseas',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="fixed_assets_overseas"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
        ],
      },
      {
        title: '股权投资计划额',
        align: 'center',
        children: [
          {
            title: '境内',
            dataIndex: 'equity_investment_domestic',
            key: 'equity_investment_domestic',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="equity_investment_domestic"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
          {
            title: '境外',
            dataIndex: 'equity_investment_overseas',
            key: 'equity_investment_overseas',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="equity_investment_overseas"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
        ],
      },
      {
        title: '其中：并购计划额',
        align: 'center',
        children: [
          {
            title: '境内',
            dataIndex: 'cross_border_domestic',
            key: 'cross_border_domestic',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="cross_border_domestic"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
          {
            title: '境外',
            dataIndex: 'cross_border_overseas',
            key: 'cross_border_overseas',
            minWidth: 150,
            align: 'center',
            render: (value, record) => (
              <NumberInput
                value={value}
                record={record}
                field="cross_border_overseas"
                disabled={isDisabled}
                onChange={handleInputChange}
              />
            ),
          },
        ],
      },
      {
        title: '备注',
        align: 'center',
        children: [
          {
            title: (
              <span className="text-[#FF4C4C]">
                注意：应与表5和表6的“所属战新产业”数据一致
              </span>
            ),
            dataIndex: 'remarks',
            key: 'remarks',
            minWidth: 320,
            align: 'center',
            render: (value, record) => (
              <RemarkInput
                value={value || ''}
                record={record}
                onChange={handleInputChange}
              />
            ),
          },
        ],
      },
    ]
  }, [handleInputChange, summaryData.consolidation])

  const title =
    InvestmentPlanTableNamesMap[
      tableName as keyof typeof InvestmentPlanTableNamesMap
    ]
  return (
    <div className="flex h-full flex-col">
      {contextHolder}

      <Card
        title={title}
        extra={
          isConsolidatedReport(summaryData.consolidation) ? null : (
            <Button
              color="primary"
              icon={<RefreshCcw className="mt-[4px] size-4" />}
              variant="text"
              onClick={() => {
                mutate({
                  company_id: company?.id || '',
                  year: summaryData?.investment_year || '',
                })
              }}
            >
              刷新数据
            </Button>
          )
        }
      >
        <div className="mb-4 flex items-center justify-between">
          <p>填报年份：{summaryData?.investment_year}</p>
          <p>编制单位：{summaryData?.company_name}</p>
          <p>金额：万元</p>
        </div>
        <Table
          size="small"
          tableLayout="auto"
          bordered
          loading={isLoading || isPending || saveIsPending}
          className={styles.customTable}
          dataSource={tableData}
          columns={columns}
          pagination={false}
          scroll={{ x: 'max-content' }}
          sticky={{ offsetHeader: 48 }}
          rowKey="style"
        />
        <div className="mt-4 flex justify-end gap-2">
          <Button type="primary" onClick={handleSubmit} loading={saveIsPending}>
            保存数据
          </Button>
          <Link
            to="/data-summary/investment-plan/table/$tableName/list"
            params={{ tableName: tableName ?? '' }}
          >
            <Button>返回</Button>
          </Link>
        </div>
      </Card>
    </div>
  )
}
