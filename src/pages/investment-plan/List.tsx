import { useParams } from '@tanstack/react-router'

import { InvestmentPlanTableMap } from './enum/InvestmentPlan'

export function TableList() {
  const params = useParams({ strict: false })
  const tableName = params.tableName as keyof typeof InvestmentPlanTableMap
  const TableComponent =
    tableName && tableName in InvestmentPlanTableMap
      ? InvestmentPlanTableMap[tableName]
      : null

  if (!TableComponent) {
    return null
  }

  return <TableComponent />
}
