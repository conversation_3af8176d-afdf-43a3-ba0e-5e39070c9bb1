import { useMutation } from '@tanstack/react-query'
import { Input, Modal, Row, Col, message } from 'antd'
import { useState } from 'react'

import { request, type APIResponse } from '@/lib/request'

export const RejectModal = ({
  open,
  setOpen,
  rejectKey,
  rejectUrl,
}: {
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
  rejectKey: string
  rejectUrl: string
}) => {
  const [rejectReason, setRejectReason] = useState('')

  const handleReject = useMutation({
    mutationKey: [rejectUrl, rejectKey],
    mutationFn: async () => {
      if (!rejectReason) {
        message.error('请输入驳回原因')
        return
      }

      const res = await request<APIResponse<string>>(rejectUrl, {
        method: 'POST',
        body: {
          approval_node_ids: [rejectKey],
          reject_reason: rejectReason,
        },
      })
      if (res.code === 200001) {
        message.success('操作成功')
        setOpen(false)
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  return (
    <Modal
      title="驳回数据"
      open={open}
      onCancel={() => setOpen(false)}
      onOk={() => handleReject.mutate()}
      loading={handleReject.isPending}
    >
      <Row>
        <Col span={8}>驳回原因</Col>
        <Col span={16}>
          <Input.TextArea
            required
            className="w-full"
            placeholder="请输入驳回原因"
            autoSize={{ minRows: 3, maxRows: 5 }}
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
          />
        </Col>
      </Row>
    </Modal>
  )
}
