import { useMutation } from '@tanstack/react-query'
import { Col, DatePicker, message, Modal, Row, Spin } from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'

import { type APIResponse, request } from '@/lib/request'

export const ReportModal = ({
  open,
  setOpen,
  selectedKeys,
  reportUrl,
  reportMonths,
}: {
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
  selectedKeys: string[]
  reportUrl: string
  reportMonths: Array<string | undefined>
}) => {
  const [selectedMonth, setSelectedMonth] = useState<dayjs.Dayjs | null>(null)

  const getLatestMonth = () => {
    const months = (reportMonths ?? [])
      .filter((m): m is string => typeof m === 'string' && m.length > 0)
      .map((m) => dayjs(m))
      .filter((d) => d.isValid())
    let latestMonth: dayjs.Dayjs | null = null
    if (months.length > 0) {
      const sorted = [...months].sort((a, b) => b.valueOf() - a.valueOf())
      latestMonth = sorted[0]
    }
    return latestMonth
  }

  const handleReport = useMutation({
    mutationKey: [reportUrl, selectedKeys],
    mutationFn: async () => {
      if (!selectedMonth) {
        message.error('请选择上报月份')
        return
      }

      const res = await request<APIResponse<string>>(reportUrl, {
        method: 'POST',
        body: {
          approval_node_ids: selectedKeys,
          report_month: dayjs(selectedMonth)
            .startOf('month')
            .format('YYYY-MM-DD HH:mm:ss'),
        },
      })
      if (res.code === 200001) {
        message.success('操作成功')
        setOpen(false)
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  return (
    <Modal
      title="数据上报"
      open={open}
      onCancel={() => setOpen(false)}
      onOk={() => handleReport.mutate()}
    >
      <Spin spinning={handleReport.isPending}>
        <Row>
          <Col span={8}>请选择上报月份</Col>
          <Col span={16}>
            <DatePicker
              className="w-full"
              picker="month"
              value={selectedMonth}
              required
              onChange={(value) => setSelectedMonth(value)}
              disabledDate={(date) => {
                const latestMonth = getLatestMonth()
                return latestMonth
                  ? date.valueOf() < latestMonth.valueOf()
                  : false
              }}
            />
          </Col>
        </Row>
      </Spin>
    </Modal>
  )
}
