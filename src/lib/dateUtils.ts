/**
 * 从季度字符串（如 "2025-Q1"）中提取年份和季度
 * @param quarterStr - 季度字符串，格式为 "YYYY-QN"（N为1-4的数字）
 * @returns 包含年份和季度的对象，格式 { year: number; quarter: number }
 * @throws 当输入格式不符合要求时抛出错误
 */
export function extractYearAndQuarter(quarterStr: string): {
  year: number
  quarter: number
} {
  const regex = /^(\d{4})-Q([1-4])$/
  const match = quarterStr.match(regex)

  if (!match) {
    throw new Error(
      `无效的季度格式: ${quarterStr}，请使用 "YYYY-QN" 格式（如 "2025-Q1"）`,
    )
  }

  const year = parseInt(match[1], 10)
  const quarter = parseInt(match[2], 10)

  return { year, quarter }
}
